import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:uuid/uuid.dart';
import '../ScrapVault/CrrAiMaster83.dart';
import '../ScrapVault/CrrChatModel29.dart';
import '../ScrapVault/CrrUserModel47.dart';
import '../ReclaimFlow/CrrUserProvider52.dart';
import '../RemakeParts/CrrNeumorphic93.dart';
import '../UpcycleMaster/CrrConversationService84.dart';

/// AI对话聊天页面
/// 实现与AI角色的实时对话，包含打字机效果和新拟物化设计
class CrrChatWorkshop24 extends StatefulWidget {
  final CrrAiMaster83 master;
  final String? existingSessionId; // 现有会话ID，用于加载历史记录

  const CrrChatWorkshop24({
    super.key,
    required this.master,
    this.existingSessionId,
  });

  @override
  State<CrrChatWorkshop24> createState() => _CrrChatWorkshop24State();
}

class _CrrChatWorkshop24State extends State<CrrChatWorkshop24>
    with TickerProviderStateMixin {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<CrrChatMessage91> _messages = [];
  final CrrConversationService84 _conversationService = CrrConversationService84();

  late AnimationController _headerAnimController;
  late AnimationController _typingAnimController;

  bool _isTyping = false;
  bool _canSendMessage = true;
  String? _currentSessionId;
  CrrChatSession56? _currentSession;

  @override
  void initState() {
    super.initState();
    _headerAnimController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _typingAnimController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _initializeChat();
  }

  void _initializeChat() {
    _headerAnimController.forward();

    // 初始化会话ID
    _currentSessionId = widget.existingSessionId ?? const Uuid().v4();

    // 加载历史聊天记录或添加欢迎消息
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.existingSessionId != null) {
        _loadExistingConversation();
      } else {
        _addWelcomeMessage();
      }
    });
  }

  /// 加载现有对话记录
  Future<void> _loadExistingConversation() async {
    try {
      final session = await _conversationService.getSessionById(_currentSessionId!);
      if (session != null) {
        setState(() {
          _currentSession = session;
          _messages.clear();
          _messages.addAll(session.messages);
        });
        _scrollToBottom();
      } else {
        // 如果找不到会话，添加欢迎消息
        _addWelcomeMessage();
      }
    } catch (e) {
      debugPrint('加载对话记录失败: $e');
      _addWelcomeMessage();
    }
  }

  void _addWelcomeMessage() {
    final welcomeMessage = CrrChatMessage91.createAiMessage(
      chatSessionId: _currentSessionId!,
      masterId: widget.master.masterId,
      content: _getWelcomeMessage(),
    );

    setState(() {
      _messages.add(welcomeMessage);
    });

    // 保存欢迎消息到会话
    _saveMessageToSession(welcomeMessage);
    _scrollToBottom();
  }

  String _getWelcomeMessage() {
    switch (widget.master.masterId) {
      case 'master_001':
        return "Hello! I'm your Carton Upcycle Creator. Ready to transform those cardboard boxes into amazing storage solutions and playhouses? What cardboard items do you have lying around?";
      case 'master_002':
        return "Hi there! I'm here to help you turn plastic bottles into useful items. From planters to organizers, let's give those bottles a second life! What plastic bottles do you want to upcycle?";
      case 'master_003':
        return "Welcome! I specialize in glass jar transformations. Whether it's candle holders or storage containers, I'll guide you through beautiful glass upcycling projects. What glass jars do you have?";
      default:
        return "Hello! I'm ${widget.master.name}. I'm excited to help you with creative upcycling projects. ${widget.master.shortDescription} What would you like to create today?";
    }
  }

  @override
  void dispose() {
    _headerAnimController.dispose();
    _typingAnimController.dispose();
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CrrNeumorphic93.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            _buildNeumorphicHeader(),
            Expanded(child: _buildChatArea()),
            _buildMessageInput(),
          ],
        ),
      ),
    );
  }

  /// 构建新拟物化头部
  Widget _buildNeumorphicHeader() {
    return CrrNeumorphic93.card(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      borderRadius: 20,
      child: Row(
        children: [
          // 返回按钮
          CrrNeumorphic93.container(
            width: 40,
            height: 40,
            borderRadius: 20,
            onTap: () => Navigator.pop(context),
            child: Icon(
              Icons.arrow_back_ios_new,
              color: CrrNeumorphic93.textSecondary,
              size: 20,
            ),
          ),
          
          const SizedBox(width: 16),
          
          // AI角色头像
          CrrNeumorphic93.container(
            width: 50,
            height: 50,
            borderRadius: 25,
            isInset: true,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(25),
              child: Image.asset(
                widget.master.avatarPath,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: CrrNeumorphic93.primaryColor.withOpacity(0.3),
                    child: Icon(
                      Icons.person,
                      color: CrrNeumorphic93.textSecondary,
                      size: 25,
                    ),
                  );
                },
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // AI角色信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.master.name,
                  style: TextStyle(
                    color: CrrNeumorphic93.textPrimary,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  widget.master.category,
                  style: TextStyle(
                    color: CrrNeumorphic93.textSecondary,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          
          // 免费次数显示
          Consumer<CrrUserProvider52>(
            builder: (context, userProvider, child) {
              return CrrNeumorphic93.container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                borderRadius: 12,
                backgroundColor: widget.master.freeChatsRemaining > 0
                    ? CrrNeumorphic93.primaryColor
                    : CrrNeumorphic93.accentColor,
                child: Text(
                  '${widget.master.freeChatsRemaining} free',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    ).animate(controller: _headerAnimController)
      .fadeIn(duration: 600.ms)
      .slideY(begin: -0.3, end: 0);
  }

  /// 构建聊天区域
  Widget _buildChatArea() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: CrrNeumorphic93.container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        borderRadius: 20,
        isInset: true,
        child: ListView.builder(
          controller: _scrollController,
          itemCount: _messages.length + (_isTyping ? 1 : 0),
          itemBuilder: (context, index) {
            if (index == _messages.length && _isTyping) {
              return _buildTypingIndicator();
            }
            
            final message = _messages[index];
            return _buildMessageBubble(message, index);
          },
        ),
      ),
    );
  }

  /// 构建消息气泡
  Widget _buildMessageBubble(CrrChatMessage91 message, int index) {
    final isUser = message.isFromUser;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            // AI头像
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: CrrNeumorphic93.getElevatedShadow(),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: Image.asset(
                  widget.master.avatarPath,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: CrrNeumorphic93.primaryColor,
                      child: const Icon(
                        Icons.smart_toy,
                        color: Colors.white,
                        size: 16,
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(width: 8),
          ],
          
          // 消息内容
          Flexible(
            child: CrrNeumorphic93.container(
              padding: const EdgeInsets.all(12),
              borderRadius: 16,
              backgroundColor: isUser 
                  ? CrrNeumorphic93.primaryColor 
                  : CrrNeumorphic93.cardColor,
              child: Text(
                message.content,
                style: TextStyle(
                  color: isUser 
                      ? Colors.white 
                      : CrrNeumorphic93.textPrimary,
                  fontSize: 14,
                  height: 1.4,
                ),
              ),
            ),
          ),
          
          if (isUser) ...[
            const SizedBox(width: 8),
            // 用户头像
            Consumer<CrrUserProvider52>(
              builder: (context, userProvider, child) {
                final user = userProvider.currentUser;
                return Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: CrrNeumorphic93.getElevatedShadow(),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: user?.avatarPath != null
                        ? Image.asset(
                            user!.avatarPath,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: CrrNeumorphic93.accentColor,
                                child: const Icon(
                                  Icons.person,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              );
                            },
                          )
                        : Container(
                            color: CrrNeumorphic93.accentColor,
                            child: const Icon(
                              Icons.person,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                  ),
                );
              },
            ),
          ],
        ],
      ),
    ).animate()
      .fadeIn(duration: 400.ms, delay: (index * 100).ms)
      .slideY(begin: 0.3, end: 0);
  }

  /// 构建打字指示器
  Widget _buildTypingIndicator() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          // AI头像
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: CrrNeumorphic93.getElevatedShadow(),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Image.asset(
                widget.master.avatarPath,
                fit: BoxFit.cover,
              ),
            ),
          ),
          
          const SizedBox(width: 8),
          
          // 打字动画
          CrrNeumorphic93.container(
            padding: const EdgeInsets.all(12),
            borderRadius: 16,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildTypingDot(0),
                const SizedBox(width: 4),
                _buildTypingDot(1),
                const SizedBox(width: 4),
                _buildTypingDot(2),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建打字动画点
  Widget _buildTypingDot(int index) {
    return Container(
      width: 8,
      height: 8,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: CrrNeumorphic93.textSecondary,
      ),
    ).animate(controller: _typingAnimController)
      .scale(
        begin: const Offset(0.5, 0.5),
        end: const Offset(1.0, 1.0),
        delay: (index * 200).ms,
        duration: 600.ms,
      );
  }

  /// 构建消息输入区域
  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // 输入框
          Expanded(
            child: CrrNeumorphic93.textField(
              controller: _messageController,
              hintText: 'Type your message...',
              borderRadius: 20,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // 发送按钮
          CrrNeumorphic93.container(
            width: 48,
            height: 48,
            borderRadius: 24,
            backgroundColor: _canSendMessage 
                ? CrrNeumorphic93.primaryColor 
                : CrrNeumorphic93.textSecondary,
            onTap: _canSendMessage ? _sendMessage : null,
            child: Icon(
              Icons.send,
              color: Colors.white,
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  /// 发送消息
  void _sendMessage() async {
    final messageText = _messageController.text.trim();
    if (messageText.isEmpty || !_canSendMessage) return;

    // 检查免费次数 - 只显示提示，不阻止发送
    final userProvider = context.read<CrrUserProvider52>();
    final currentUser = userProvider.currentUser;

    if (currentUser == null) return;

    // 添加用户消息
    final userMessage = CrrChatMessage91.createUserMessage(
      chatSessionId: _currentSessionId!,
      masterId: widget.master.masterId,
      content: messageText,
    );

    setState(() {
      _messages.add(userMessage);
      _isTyping = true;
      _canSendMessage = false;
    });

    // 立即清空输入框
    _messageController.clear();
    _scrollToBottom();
    _typingAnimController.repeat();

    // 保存用户消息到会话
    await _saveMessageToSession(userMessage);

    try {
      // 调用真实的AI API
      final aiResponse = await _conversationService.sendMessageToAi(
        userMessage: messageText,
        master: widget.master,
        conversationHistory: _messages.where((m) => m != userMessage).toList(),
      );

      final aiMessage = CrrChatMessage91.createAiMessage(
        chatSessionId: _currentSessionId!,
        masterId: widget.master.masterId,
        content: aiResponse,
      );

      setState(() {
        _messages.add(aiMessage);
        _isTyping = false;
        _canSendMessage = true;
      });

      _typingAnimController.stop();
      _scrollToBottom();

      // 保存AI回复到会话
      await _saveMessageToSession(aiMessage);

      // 只有在AI成功回复后才扣除次数
      await userProvider.incrementChatCount(widget.master.masterId);

    } catch (e) {
      // API调用失败时的处理
      setState(() {
        _isTyping = false;
        _canSendMessage = true;
      });

      _typingAnimController.stop();

      // 显示免费次数用完的提示（如果适用）
      if (widget.master.freeChatsRemaining <= 0) {
        _showMessage('免费次数已用完，请购买金币继续聊天');
      } else {
        _showMessage('AI服务暂时不可用，请稍后重试');
      }

      debugPrint('AI回复失败: $e');
    }
  }

  /// 保存消息到会话
  Future<void> _saveMessageToSession(CrrChatMessage91 message) async {
    try {
      final userProvider = context.read<CrrUserProvider52>();
      final currentUser = userProvider.currentUser;

      if (currentUser == null) return;

      // 更新或创建会话
      if (_currentSession == null) {
        // 创建新会话
        _currentSession = CrrChatSession56(
          sessionId: _currentSessionId!,
          masterId: widget.master.masterId,
          userId: currentUser.userId,
          title: _generateSessionTitle(message.content),
          createdAt: DateTime.now(),
          lastMessageAt: message.timestamp,
          messages: [message],
          isActive: true,
        );
      } else {
        // 更新现有会话
        _currentSession = _currentSession!.addMessage(message);
      }

      // 保存到本地存储
      await _conversationService.saveConversationSession(_currentSession!);
    } catch (e) {
      debugPrint('保存消息失败: $e');
    }
  }

  /// 生成会话标题
  String _generateSessionTitle(String firstMessage) {
    // 使用第一条用户消息的前30个字符作为标题
    final title = firstMessage.length > 30
        ? '${firstMessage.substring(0, 30)}...'
        : firstMessage;
    return title.isEmpty ? 'Chat with ${widget.master.name}' : title;
  }

  /// 滚动到底部
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  /// 显示消息
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: CrrNeumorphic93.accentColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
