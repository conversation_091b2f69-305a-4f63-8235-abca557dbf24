import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 金币管理器 - 全局金币状态管理和持久化
class CrrCoinManager78 extends ChangeNotifier {
  static const String _coinKey = 'crr_user_coin_balance';
  
  int _coinBalance = 0;
  bool _isLoading = false;

  /// 获取当前金币余额
  int get coinBalance => _coinBalance;

  /// 是否正在加载
  bool get isLoading => _isLoading;

  /// 格式化金币显示
  String get formattedBalance => _coinBalance.toString();

  /// 单例实例
  static CrrCoinManager78? _instance;
  static CrrCoinManager78 get instance {
    _instance ??= CrrCoinManager78._internal();
    return _instance!;
  }

  CrrCoinManager78._internal();

  /// 初始化 - 从本地读取金币余额
  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      _coinBalance = prefs.getInt(_coinKey) ?? 0;
      
      if (kDebugMode) {
        print('CrrCoinManager78: Loaded coin balance: $_coinBalance');
      }
    } catch (e) {
      if (kDebugMode) {
        print('CrrCoinManager78: Error loading coin balance: $e');
      }
      _coinBalance = 0;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 添加金币
  Future<bool> addCoins(int amount) async {
    if (amount <= 0) return false;

    try {
      final newBalance = _coinBalance + amount;
      final success = await _saveCoinBalance(newBalance);
      
      if (success) {
        _coinBalance = newBalance;
        notifyListeners();
        
        if (kDebugMode) {
          print('CrrCoinManager78: Added $amount coins, new balance: $_coinBalance');
        }
        return true;
      }
    } catch (e) {
      if (kDebugMode) {
        print('CrrCoinManager78: Error adding coins: $e');
      }
    }
    return false;
  }

  /// 消费金币
  Future<bool> spendCoins(int amount) async {
    if (amount <= 0 || _coinBalance < amount) return false;

    try {
      final newBalance = _coinBalance - amount;
      final success = await _saveCoinBalance(newBalance);
      
      if (success) {
        _coinBalance = newBalance;
        notifyListeners();
        
        if (kDebugMode) {
          print('CrrCoinManager78: Spent $amount coins, new balance: $_coinBalance');
        }
        return true;
      }
    } catch (e) {
      if (kDebugMode) {
        print('CrrCoinManager78: Error spending coins: $e');
      }
    }
    return false;
  }

  /// 检查是否有足够金币
  bool hasEnoughCoins(int amount) => _coinBalance >= amount;

  /// 设置金币余额（仅用于测试或特殊情况）
  Future<bool> setCoinBalance(int balance) async {
    if (balance < 0) return false;

    try {
      final success = await _saveCoinBalance(balance);
      if (success) {
        _coinBalance = balance;
        notifyListeners();
        
        if (kDebugMode) {
          print('CrrCoinManager78: Set coin balance to: $_coinBalance');
        }
        return true;
      }
    } catch (e) {
      if (kDebugMode) {
        print('CrrCoinManager78: Error setting coin balance: $e');
      }
    }
    return false;
  }

  /// 保存金币余额到本地
  Future<bool> _saveCoinBalance(int balance) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setInt(_coinKey, balance);
    } catch (e) {
      if (kDebugMode) {
        print('CrrCoinManager78: Error saving coin balance: $e');
      }
      return false;
    }
  }

  /// 刷新金币余额（从本地重新读取）
  Future<void> refresh() async {
    await initialize();
  }

  /// 清除所有金币数据（仅用于测试）
  Future<void> clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_coinKey);
      _coinBalance = 0;
      notifyListeners();
      
      if (kDebugMode) {
        print('CrrCoinManager78: Cleared all coin data');
      }
    } catch (e) {
      if (kDebugMode) {
        print('CrrCoinManager78: Error clearing coin data: $e');
      }
    }
  }
}
