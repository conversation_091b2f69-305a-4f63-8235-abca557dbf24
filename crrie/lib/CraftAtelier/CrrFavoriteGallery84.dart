import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:math' as math;
import '../ScrapVault/CrrAiMaster83.dart';
import '../ReclaimFlow/CrrUserProvider52.dart';
import '../RemakeParts/CrrNeumorphic93.dart';
import 'CrrMasterDetail56.dart';
import 'CrrChatWorkshop24.dart';

/// 收藏角色专家画廊页面
/// 采用创新的蜂窝状布局和动态交互设计
class CrrFavoriteGallery84 extends StatefulWidget {
  const CrrFavoriteGallery84({super.key});

  @override
  State<CrrFavoriteGallery84> createState() => _CrrFavoriteGallery84State();
}

class _CrrFavoriteGallery84State extends State<CrrFavoriteGallery84>
    with TickerProviderStateMixin {
  late AnimationController _headerAnimController;
  late AnimationController _gridAnimController;
  late AnimationController _floatingAnimController;
  
  final ScrollController _scrollController = ScrollController();
  
  @override
  void initState() {
    super.initState();
    _headerAnimController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _gridAnimController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _floatingAnimController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _initializeAnimations();
  }
  
  void _initializeAnimations() {
    _headerAnimController.forward();
    _gridAnimController.forward();
    _floatingAnimController.repeat();
  }
  
  @override
  void dispose() {
    _headerAnimController.dispose();
    _gridAnimController.dispose();
    _floatingAnimController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CrrNeumorphic93.backgroundColor,
      body: SafeArea(
        child: Stack(
          children: [
            // 背景装饰
            _buildBackgroundDecoration(),
            
            // 主要内容
            Column(
              children: [
                _buildDynamicHeader(),
                Expanded(child: _buildFavoriteContent()),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建背景装饰
  Widget _buildBackgroundDecoration() {
    return Positioned.fill(
      child: AnimatedBuilder(
        animation: _floatingAnimController,
        builder: (context, child) {
          return CustomPaint(
            painter: HexagonBackgroundPainter(
              animationValue: _floatingAnimController.value,
            ),
          );
        },
      ),
    );
  }
  
  /// 构建动态头部
  Widget _buildDynamicHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // 返回按钮
          CrrNeumorphic93.container(
            width: 48,
            height: 48,
            borderRadius: 24,
            onTap: () => Navigator.pop(context),
            child: const Icon(
              Icons.arrow_back_ios_new,
              color: CrrNeumorphic93.textPrimary,
              size: 20,
            ),
          ).animate(controller: _headerAnimController)
            .slideX(begin: -1, end: 0, duration: 600.ms)
            .fadeIn(duration: 600.ms),
          
          const SizedBox(width: 16),
          
          // 标题区域
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'My Experts',
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: CrrNeumorphic93.textPrimary,
                  ),
                ).animate(controller: _headerAnimController)
                  .slideY(begin: -0.5, end: 0, duration: 600.ms, delay: 200.ms)
                  .fadeIn(duration: 600.ms, delay: 200.ms),
                
                // const SizedBox(height: 4),
                //
                // Consumer<CrrUserProvider52>(
                //   builder: (context, userProvider, child) {
                //     final favoriteCount = userProvider.currentUser?.favoriteCount ?? 0;
                //     return Text(
                //       '$favoriteCount experts in your collection',
                //       style: const TextStyle(
                //         fontSize: 14,
                //         color: CrrNeumorphic93.textSecondary,
                //       ),
                //     ).animate(controller: _headerAnimController)
                //       .slideY(begin: 0.5, end: 0, duration: 600.ms, delay: 400.ms)
                //       .fadeIn(duration: 600.ms, delay: 400.ms);
                //   },
                // ),
              ],
            ),
          ),
          
          // 空占位符
          const SizedBox.shrink(),
        ],
      ),
    );
  }
  

  
  /// 构建收藏内容
  Widget _buildFavoriteContent() {
    return Consumer<CrrUserProvider52>(
      builder: (context, userProvider, child) {
        final user = userProvider.currentUser;
        final favoriteIds = user?.favoriteMasterIds ?? [];
        
        if (favoriteIds.isEmpty) {
          return _buildEmptyState();
        }
        
        // 获取收藏的AI角色
        final allMasters = CrrAiMasterRepo92.getAllMasters();
        final favoriteMasters = allMasters
            .where((master) => favoriteIds.contains(master.masterId))
            .toList();
        
        if (favoriteMasters.isEmpty) {
          return _buildNoResultsState();
        }

        return _buildHexagonGrid(favoriteMasters);
      },
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [
                  CrrNeumorphic93.primaryColor.withOpacity(0.3),
                  CrrNeumorphic93.accentColor.withOpacity(0.3),
                ],
              ),
            ),
            child: const Icon(
              Icons.face_outlined,
              size: 60,
              color: CrrNeumorphic93.textSecondary,
            ),
          ).animate(controller: _gridAnimController)
            .scale(begin: const Offset(0.5, 0.5), end: const Offset(1, 1))
            .fadeIn(duration: 800.ms),

          const SizedBox(height: 24),

          // Text(
          //   'No Favorite Experts Yet',
          //   style: const TextStyle(
          //     fontSize: 24,
          //     fontWeight: FontWeight.bold,
          //     color: CrrNeumorphic93.textPrimary,
          //   ),
          // ).animate(controller: _gridAnimController)
          //   .slideY(begin: 0.3, end: 0, duration: 800.ms, delay: 200.ms)
          //   .fadeIn(duration: 800.ms, delay: 200.ms),
          //
          // const SizedBox(height: 12),

          Text(
            'Start exploring AI experts and add them\nto your favorites collection',
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 16,
              color: CrrNeumorphic93.textSecondary,
            ),
          ).animate(controller: _gridAnimController)
            .slideY(begin: 0.3, end: 0, duration: 800.ms, delay: 400.ms)
            .fadeIn(duration: 800.ms, delay: 400.ms),

          // const SizedBox(height: 32),
          //
          // CrrNeumorphic93.container(
          //   padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          //   borderRadius: 25,
          //   backgroundColor: CrrNeumorphic93.primaryColor,
          //   onTap: () => Navigator.pop(context),
          //   child: const Text(
          //     'Explore Experts',
          //     style: TextStyle(
          //       color: Colors.white,
          //       fontWeight: FontWeight.w600,
          //       fontSize: 16,
          //     ),
          //   ),
          // ).animate(controller: _gridAnimController)
          //   .slideY(begin: 0.3, end: 0, duration: 800.ms, delay: 600.ms)
          //   .fadeIn(duration: 800.ms, delay: 600.ms),
        ],
      ),
    );
  }

  /// 构建无结果状态
  Widget _buildNoResultsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_border,
            size: 80,
            color: CrrNeumorphic93.textSecondary.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No favorite experts yet',
            style: const TextStyle(
              fontSize: 18,
              color: CrrNeumorphic93.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start exploring and add experts to your favorites',
            style: const TextStyle(
              fontSize: 14,
              color: CrrNeumorphic93.textSecondary,
            ),
          ),
        ],
      ),
    ).animate(controller: _gridAnimController)
      .fadeIn(duration: 800.ms)
      .slideY(begin: 0.3, end: 0, duration: 800.ms);
  }

  /// 构建蜂窝状网格布局
  Widget _buildHexagonGrid(List<CrrAiMaster83> masters) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          SliverGrid(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.75, // 调整比例，让卡片更高
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final master = masters[index];
                return _buildHexagonCard(master, index);
              },
              childCount: masters.length,
            ),
          ),
        ],
      ),
    );
  }



  /// 构建蜂窝状卡片
  Widget _buildHexagonCard(CrrAiMaster83 master, int index) {
    return CrrNeumorphic93.container(
      borderRadius: 20,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 头像和收藏按钮区域
          SizedBox(
            height: 90,
            child: Stack(
              children: [
                // 主头像
                Center(
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      boxShadow: CrrNeumorphic93.getElevatedShadow(),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(40),
                      child: Image.asset(
                        master.avatarPath,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: CrrNeumorphic93.primaryColor.withOpacity(0.3),
                            child: const Icon(
                              Icons.person,
                              color: CrrNeumorphic93.textSecondary,
                              size: 40,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),

                // 收藏按钮
                Positioned(
                  top: 0,
                  right: 0,
                  child: Consumer<CrrUserProvider52>(
                    builder: (context, userProvider, child) {
                      final user = userProvider.currentUser;
                      final isFavorited = user?.isMasterFavorited(master.masterId) ?? false;

                      return CrrNeumorphic93.container(
                        width: 32,
                        height: 32,
                        borderRadius: 16,
                        backgroundColor: isFavorited
                            ? Colors.red.withOpacity(0.1)
                            : CrrNeumorphic93.cardColor,
                        onTap: () => _toggleFavorite(master.masterId),
                        child: Icon(
                          isFavorited ? Icons.favorite : Icons.favorite_border,
                          color: isFavorited ? Colors.red : CrrNeumorphic93.textSecondary,
                          size: 16,
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // 角色名称
          Text(
            master.name,
            style: const TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.bold,
              color: CrrNeumorphic93.textPrimary,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 8),

          // 分类标签
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
            decoration: BoxDecoration(
              color: CrrNeumorphic93.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              master.category,
              style: const TextStyle(
                fontSize: 11,
                color: CrrNeumorphic93.primaryColor,
                fontWeight: FontWeight.w600,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          const Spacer(),

          // 操作按钮 - 改为圆形图标按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              CrrNeumorphic93.container(
                width: 36,
                height: 36,
                borderRadius: 18,
                backgroundColor: CrrNeumorphic93.accentColor,
                onTap: () => _navigateToChat(master),
                child: const Icon(
                  Icons.chat_bubble_rounded,
                  color: Colors.white,
                  size: 18,
                ),
              ),
              CrrNeumorphic93.container(
                width: 36,
                height: 36,
                borderRadius: 18,
                backgroundColor: CrrNeumorphic93.primaryColor,
                onTap: () => _navigateToDetail(master),
                child: const Icon(
                  Icons.info_rounded,
                  color: Colors.white,
                  size: 18,
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate(controller: _gridAnimController)
      .slideY(
        begin: 0.5,
        end: 0,
        duration: 600.ms,
        delay: (index * 100).ms,
      )
      .fadeIn(
        duration: 600.ms,
        delay: (index * 100).ms,
      );
  }





  /// 切换收藏状态
  void _toggleFavorite(String masterId) {
    final userProvider = context.read<CrrUserProvider52>();
    userProvider.toggleMasterFavorite(masterId);

    // 显示反馈
    final user = userProvider.currentUser;
    final isFavorited = user?.isMasterFavorited(masterId) ?? false;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isFavorited ? 'Added to favorites ❤️' : 'Removed from favorites',
        ),
        backgroundColor: CrrNeumorphic93.primaryColor,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  /// 导航到聊天页面
  void _navigateToChat(CrrAiMaster83 master) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CrrChatWorkshop24(master: master),
      ),
    );
  }

  /// 导航到详情页面
  void _navigateToDetail(CrrAiMaster83 master) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CrrMasterDetail56(master: master),
      ),
    );
  }

  /// 显示角色快速操作
  void _showMasterQuickActions(CrrAiMaster83 master) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: CrrNeumorphic93.backgroundColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              master.name,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: CrrNeumorphic93.textPrimary,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: CrrNeumorphic93.container(
                    height: 50,
                    borderRadius: 25,
                    backgroundColor: CrrNeumorphic93.accentColor,
                    onTap: () {
                      Navigator.pop(context);
                      _navigateToChat(master);
                    },
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.chat_bubble, color: Colors.white, size: 20),
                        SizedBox(width: 8),
                        Text('Chat', style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600)),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: CrrNeumorphic93.container(
                    height: 50,
                    borderRadius: 25,
                    backgroundColor: CrrNeumorphic93.primaryColor,
                    onTap: () {
                      Navigator.pop(context);
                      _navigateToDetail(master);
                    },
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.info_outline, color: Colors.white, size: 20),
                        SizedBox(width: 8),
                        Text('Details', style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600)),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// 蜂窝状背景绘制器
class HexagonBackgroundPainter extends CustomPainter {
  final double animationValue;

  HexagonBackgroundPainter({required this.animationValue});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = CrrNeumorphic93.primaryColor.withOpacity(0.05)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    final hexSize = 40.0;
    final rows = (size.height / (hexSize * 1.5)).ceil() + 2;
    final cols = (size.width / (hexSize * math.sqrt(3))).ceil() + 2;

    for (int row = -1; row < rows; row++) {
      for (int col = -1; col < cols; col++) {
        final offsetX = col * hexSize * math.sqrt(3) +
                       (row % 2) * hexSize * math.sqrt(3) / 2;
        final offsetY = row * hexSize * 1.5;

        // 添加动画偏移
        final animOffset = math.sin(animationValue * 2 * math.pi +
                                   (row + col) * 0.5) * 2;

        _drawHexagon(
          canvas,
          paint,
          Offset(offsetX + animOffset, offsetY + animOffset),
          hexSize * 0.8,
        );
      }
    }
  }

  void _drawHexagon(Canvas canvas, Paint paint, Offset center, double size) {
    final path = Path();
    for (int i = 0; i < 6; i++) {
      final angle = (i * math.pi) / 3;
      final x = center.dx + size * math.cos(angle);
      final y = center.dy + size * math.sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
