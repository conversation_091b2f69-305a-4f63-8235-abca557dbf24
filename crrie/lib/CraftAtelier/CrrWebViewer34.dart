import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../RemakeParts/CrrNeumorphic93.dart';

/// Web浏览器页面
/// 用于在应用内浏览网页内容，如隐私协议等
class CrrWebViewer34 extends StatefulWidget {
  final String url;
  final String title;

  const CrrWebViewer34({
    super.key,
    required this.url,
    required this.title,
  });

  @override
  State<CrrWebViewer34> createState() => _CrrWebViewer34State();
}

class _CrrWebViewer34State extends State<CrrWebViewer34> {
  late final WebViewController _controller;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  @override
  void dispose() {
    // 清理WebView控制器，防止内存泄漏
    super.dispose();
  }

  /// 初始化WebView
  void _initializeWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // 更新加载进度
          },
          onPageStarted: (String url) {
            if (mounted) {
              setState(() {
                _isLoading = true;
                _error = null;
              });
            }
          },
          onPageFinished: (String url) {
            if (mounted) {
              setState(() {
                _isLoading = false;
              });
            }
          },
          onWebResourceError: (WebResourceError error) {
            if (mounted) {
              setState(() {
                _isLoading = false;
                _error = 'Failed to load page: ${error.description}';
              });
            }
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CrrNeumorphic93.backgroundColor,
      appBar: AppBar(
        backgroundColor: CrrNeumorphic93.backgroundColor,
        elevation: 0,
        automaticallyImplyLeading: false,
        title: Text(
          widget.title,
          style: const TextStyle(
            color: CrrNeumorphic93.textPrimary,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        leading: CrrNeumorphic93.container(
          margin: const EdgeInsets.all(8),
          borderRadius: 12,
          backgroundColor: CrrNeumorphic93.cardColor,
          onTap: () => Navigator.pop(context),
          child: const Icon(
            Icons.arrow_back_ios_new,
            color: CrrNeumorphic93.textPrimary,
            size: 18,
          ),
        ),
        actions: [
          // 刷新按钮
          CrrNeumorphic93.container(
            margin: const EdgeInsets.all(8),
            width: 40,
            height: 40,
            borderRadius: 12,
            backgroundColor: CrrNeumorphic93.cardColor,
            onTap: () {
              _controller.reload();
              if (mounted) {
                setState(() {
                  _isLoading = true;
                  _error = null;
                });
              }
            },
            child: const Icon(
              Icons.refresh,
              color: CrrNeumorphic93.textPrimary,
              size: 18,
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          // WebView内容
          if (_error == null)
            WebViewWidget(controller: _controller)
          else
            _buildErrorView(),
          
          // 加载指示器
          if (_isLoading)
            Container(
              color: CrrNeumorphic93.backgroundColor.withOpacity(0.8),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        CrrNeumorphic93.primaryColor,
                      ),
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Loading...',
                      style: TextStyle(
                        color: CrrNeumorphic93.textSecondary,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建错误视图
  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: CrrNeumorphic93.textSecondary,
            ),
            const SizedBox(height: 16),
            const Text(
              'Failed to load page',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: CrrNeumorphic93.textPrimary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error ?? 'Unknown error occurred',
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                color: CrrNeumorphic93.textSecondary,
              ),
            ),
            const SizedBox(height: 24),
            CrrNeumorphic93.container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              borderRadius: 20,
              backgroundColor: CrrNeumorphic93.primaryColor,
              onTap: () {
                _controller.reload();
                if (mounted) {
                  setState(() {
                    _isLoading = true;
                    _error = null;
                  });
                }
              },
              child: const Text(
                'Retry',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
