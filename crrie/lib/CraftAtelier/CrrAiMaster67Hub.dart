import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../ReclaimFlow/CrrAiMasterProvider76.dart';
import '../ReclaimFlow/CrrUserProvider52.dart';
import '../ScrapVault/CrrAiMaster83.dart';

import '../RemakeParts/CrrNeumorphic93.dart';
import 'CrrChatWorkshop24.dart';

/// AI角色选择主页
/// 展示10个AI创意指导师的个性化大图卡片信息流
class CrrAiMaster67Hub extends StatefulWidget {
  const CrrAiMaster67Hub({super.key});

  @override
  State<CrrAiMaster67Hub> createState() => _CrrAiMaster67HubState();
}

class _CrrAiMaster67HubState extends State<CrrAiMaster67Hub>
    with TickerProviderStateMixin {
  late AnimationController _headerAnimController;
  final ScrollController _scrollController = ScrollController();
  
  @override
  void initState() {
    super.initState();
    _headerAnimController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // 使用WidgetsBinding.instance.addPostFrameCallback来延迟初始化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeData();
    });
  }

  Future<void> _initializeData() async {
    final userProvider = context.read<CrrUserProvider52>();

    // 只初始化用户数据，AI数据已经在Provider构造函数中加载了
    await userProvider.initializeUser();

    _headerAnimController.forward();
  }

  @override
  void dispose() {
    _headerAnimController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CrrNeumorphic93.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            _buildSimpleHeader(),
            _buildCategoryFilter(),
            Expanded(child: _buildMasterCardsList()),
          ],
        ),
      ),
    );
  }

  /// 构建简化头部
  Widget _buildSimpleHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Crrie',
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: CrrNeumorphic93.textPrimary,
                  ),
                ).animate(controller: _headerAnimController)
                  .fadeIn(duration: 600.ms)
                  .slideX(begin: -0.3, end: 0),

                const SizedBox(height: 4),

                Text(
                  'Your AI Household Waste Upcycling Guides',
                  style: TextStyle(
                    fontSize: 14,
                    color: CrrNeumorphic93.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ).animate(controller: _headerAnimController)
                  .fadeIn(duration: 600.ms, delay: 200.ms)
                  .slideX(begin: -0.3, end: 0),
              ],
            ),
          ),

          Consumer<CrrUserProvider52>(
            builder: (context, userProvider, child) {
              final user = userProvider.currentUser;
              return CrrNeumorphic93.container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                borderRadius: 20,
                backgroundColor: CrrNeumorphic93.accentColor,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.monetization_on,
                      color: Colors.white,
                      size: 18,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      '${user?.totalCoins ?? 0}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              );
            },
          ).animate(controller: _headerAnimController)
            .fadeIn(duration: 600.ms, delay: 400.ms)
            .scale(begin: const Offset(0.8, 0.8), end: const Offset(1, 1)),
        ],
      ),
    );
  }



  /// 构建分类筛选器
  Widget _buildCategoryFilter() {
    return Container(
      height: 70,
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Consumer<CrrAiMasterProvider76>(
        builder: (context, aiProvider, child) {
          final categories = aiProvider.getAllCategories();

          return ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(vertical: 8),
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              final isSelected = aiProvider.selectedCategory == category;

              return Container(
                margin: const EdgeInsets.only(right: 12),
                child: CrrNeumorphic93.container(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  borderRadius: 18,
                  isPressed: isSelected,
                  backgroundColor: isSelected
                      ? CrrNeumorphic93.primaryColor
                      : CrrNeumorphic93.cardColor,
                  onTap: () => aiProvider.filterByCategory(category),
                  child: Center(
                    child: Text(
                      category,
                      style: TextStyle(
                        color: isSelected
                            ? Colors.white
                            : CrrNeumorphic93.textPrimary,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        },
      ).animate(controller: _headerAnimController)
        .fadeIn(duration: 600.ms, delay: 800.ms)
        .slideY(begin: 0.3, end: 0),
    );
  }

  /// 构建AI角色卡片列表
  Widget _buildMasterCardsList() {
    return Consumer<CrrAiMasterProvider76>(
      builder: (context, aiProvider, child) {
        if (aiProvider.isLoading) {
          return const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(CrrNeumorphic93.primaryColor),
            ),
          );
        }

        final masters = aiProvider.filteredMasters;

        if (masters.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.search_off,
                  size: 64,
                  color: CrrNeumorphic93.textSecondary,
                ),
                const SizedBox(height: 16),
                Text(
                  'No AI masters found for this category',
                  style: TextStyle(
                    color: CrrNeumorphic93.textSecondary,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          key: const PageStorageKey('masters_list'),
          controller: _scrollController,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          itemCount: masters.length,
          cacheExtent: 1000, // 缓存更多item以减少重建
          itemBuilder: (context, index) => Container(
            key: ValueKey(masters[index].masterId), // 为每个item添加key
            margin: const EdgeInsets.only(bottom: 20),
            child: _buildLargeMasterCard(masters[index], index),
          ),
        );
      },
    );
  }

  /// 构建大尺寸AI角色卡片
  Widget _buildLargeMasterCard(CrrAiMaster83 master, int index) {
    return CrrNeumorphic93.card(
      padding: EdgeInsets.zero,
      borderRadius: 24,
      onTap: () => _navigateToChat(master),
      child: Container(
        height: 200, // 固定高度，更大的卡片
        child: Row(
          children: [
            // 左侧图片区域 - 更大的图片显示
            Expanded(
              flex: 2,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(24),
                    bottomLeft: Radius.circular(24),
                  ),
                  boxShadow: CrrNeumorphic93.getInsetShadow(),
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(24),
                    bottomLeft: Radius.circular(24),
                  ),
                  child: Stack(
                    children: [
                      // 背景图片
                      Positioned.fill(
                        child: Image.asset(
                          master.avatarPath,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: CrrNeumorphic93.primaryColor.withOpacity(0.3),
                              child: Icon(
                                Icons.person,
                                size: 60,
                                color: CrrNeumorphic93.textSecondary,
                              ),
                            );
                          },
                        ),
                      ),

                      // 免费次数标签
                      Positioned(
                        top: 12,
                        right: 12,
                        child: CrrNeumorphic93.container(
                          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                          borderRadius: 14,
                          backgroundColor: CrrNeumorphic93.primaryColor,
                          child: Text(
                            '${master.freeChatsRemaining} free',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // 右侧信息区域
            Expanded(
              flex: 3,
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // AI角色名称
                    Text(
                      master.name,
                      style: TextStyle(
                        color: CrrNeumorphic93.textPrimary,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 8),

                    // 分类标签
                    CrrNeumorphic93.container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      borderRadius: 12,
                      isInset: true,
                      child: Text(
                        master.category,
                        style: TextStyle(
                          color: CrrNeumorphic93.textSecondary,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // 描述
                    Expanded(
                      child: Text(
                        master.shortDescription,
                        style: TextStyle(
                          color: CrrNeumorphic93.textSecondary,
                          fontSize: 14,
                          height: 1.4,
                        ),
                        maxLines: 4,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                    const SizedBox(height: 12),

                    // 底部操作区域
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Tap to chat',
                          style: TextStyle(
                            color: CrrNeumorphic93.primaryColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        CrrNeumorphic93.container(
                          width: 32,
                          height: 32,
                          borderRadius: 16,
                          backgroundColor: CrrNeumorphic93.primaryColor,
                          child: const Icon(
                            Icons.arrow_forward_ios,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 导航到聊天页面
  void _navigateToChat(CrrAiMaster83 master) {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            CrrChatWorkshop24(master: master),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }
}
