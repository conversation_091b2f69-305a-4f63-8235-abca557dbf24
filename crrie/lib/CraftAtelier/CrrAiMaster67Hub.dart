import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../ReclaimFlow/CrrAiMasterProvider76.dart';
import '../ReclaimFlow/CrrUserProvider52.dart';
import '../ScrapVault/CrrAiMaster83.dart';
import '../RemakeParts/CrrSoulQuest78.dart';
import '../RemakeParts/CrrNeumorphic93.dart';
import 'CrrChatWorkshop24.dart';

/// AI角色选择主页
/// 展示10个AI创意指导师的个性化大图卡片信息流
class CrrAiMaster67Hub extends StatefulWidget {
  const CrrAiMaster67Hub({super.key});

  @override
  State<CrrAiMaster67Hub> createState() => _CrrAiMaster67HubState();
}

class _CrrAiMaster67HubState extends State<CrrAiMaster67Hub>
    with TickerProviderStateMixin {
  late AnimationController _headerAnimController;
  late AnimationController _cardsAnimController;
  final ScrollController _scrollController = ScrollController();
  
  @override
  void initState() {
    super.initState();
    _headerAnimController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _cardsAnimController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _initializeData();
  }

  Future<void> _initializeData() async {
    final aiProvider = context.read<CrrAiMasterProvider76>();
    final userProvider = context.read<CrrUserProvider52>();
    
    await Future.wait([
      aiProvider.initializeMasters(),
      userProvider.initializeUser(),
    ]);
    
    _headerAnimController.forward();
    await Future.delayed(const Duration(milliseconds: 300));
    _cardsAnimController.forward();
  }

  @override
  void dispose() {
    _headerAnimController.dispose();
    _cardsAnimController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CrrNeumorphic93.backgroundColor, // 新拟物化背景色
      body: SafeArea(
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            _buildNeumorphicHeader(),
            _buildSoulQuestModule(),
            _buildCategoryFilter(),
            _buildMasterCardsGrid(),
          ],
        ),
      ),
    );
  }

  /// 构建新拟物化头部
  Widget _buildNeumorphicHeader() {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.all(20),
        color: CrrNeumorphic93.backgroundColor,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 应用标题和金币显示
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Crrie',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: CrrNeumorphic93.textPrimary,
                        ),
                      ).animate(controller: _headerAnimController)
                        .fadeIn(duration: 600.ms)
                        .slideX(begin: -0.3, end: 0),

                      const SizedBox(height: 4),

                      Text(
                        'Your AI Household Waste Upcycling Guides',
                        style: TextStyle(
                          fontSize: 14,
                          color: CrrNeumorphic93.textSecondary,
                          fontWeight: FontWeight.w500,
                        ),
                      ).animate(controller: _headerAnimController)
                        .fadeIn(duration: 600.ms, delay: 200.ms)
                        .slideX(begin: -0.3, end: 0),
                    ],
                  ),
                ),

                Consumer<CrrUserProvider52>(
                  builder: (context, userProvider, child) {
                    final user = userProvider.currentUser;
                    return CrrNeumorphic93.container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      borderRadius: 20,
                      backgroundColor: CrrNeumorphic93.accentColor,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.monetization_on,
                            color: Colors.white,
                            size: 18,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            '${user?.totalCoins ?? 0}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ).animate(controller: _headerAnimController)
                  .fadeIn(duration: 600.ms, delay: 400.ms)
                  .scale(begin: const Offset(0.8, 0.8), end: const Offset(1, 1)),
              ],
            ),

            const SizedBox(height: 24),

            // 用户信息卡片
            Consumer<CrrUserProvider52>(
              builder: (context, userProvider, child) {
                final user = userProvider.currentUser;
                return CrrNeumorphic93.card(
                  padding: const EdgeInsets.all(20),
                  borderRadius: 20,
                  child: Row(
                    children: [
                      // 用户头像
                      CrrNeumorphic93.container(
                        width: 60,
                        height: 60,
                        borderRadius: 30,
                        isInset: true,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(30),
                          child: user?.avatarPath != null
                              ? Image.asset(
                                  user!.avatarPath,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      color: CrrNeumorphic93.primaryColor.withOpacity(0.3),
                                      child: Icon(
                                        Icons.person,
                                        color: CrrNeumorphic93.textSecondary,
                                        size: 30,
                                      ),
                                    );
                                  },
                                )
                              : Container(
                                  color: CrrNeumorphic93.primaryColor.withOpacity(0.3),
                                  child: Icon(
                                    Icons.person,
                                    color: CrrNeumorphic93.textSecondary,
                                    size: 30,
                                  ),
                                ),
                        ),
                      ),

                      const SizedBox(width: 16),

                      // 用户信息
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Welcome back, ${user?.nickname ?? 'Eco Crafter'}!',
                              style: TextStyle(
                                color: CrrNeumorphic93.textPrimary,
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              user?.userLevelTitle ?? 'Eco Beginner',
                              style: TextStyle(
                                color: CrrNeumorphic93.textSecondary,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // 等级标签
                      CrrNeumorphic93.container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        borderRadius: 12,
                        backgroundColor: CrrNeumorphic93.primaryColor,
                        child: Text(
                          'Lv.${user?.userLevel ?? 1}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ).animate(controller: _headerAnimController)
              .fadeIn(duration: 600.ms, delay: 600.ms)
              .slideY(begin: 0.3, end: 0),
          ],
        ),
      ),
    );
  }

  /// 构建灵魂提问模块
  Widget _buildSoulQuestModule() {
    return SliverToBoxAdapter(
      child: Consumer<CrrUserProvider52>(
        builder: (context, userProvider, child) {
          final user = userProvider.currentUser;
          if (user == null || !user.canAnswerSoulQuestionToday) {
            return const SizedBox.shrink();
          }
          
          return Container(
            margin: const EdgeInsets.all(20),
            child: const CrrSoulQuest78(),
          ).animate(controller: _headerAnimController)
            .fadeIn(duration: 600.ms, delay: 800.ms)
            .slideY(begin: 0.3, end: 0);
        },
      ),
    );
  }

  /// 构建分类筛选器
  Widget _buildCategoryFilter() {
    return SliverToBoxAdapter(
      child: Container(
        height: 60,
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        child: Consumer<CrrAiMasterProvider76>(
          builder: (context, aiProvider, child) {
            final categories = aiProvider.getAllCategories();

            return ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: categories.length,
              itemBuilder: (context, index) {
                final category = categories[index];
                final isSelected = aiProvider.selectedCategory == category;

                return Container(
                  margin: const EdgeInsets.only(right: 12),
                  child: CrrNeumorphic93.container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    borderRadius: 16,
                    isPressed: isSelected,
                    backgroundColor: isSelected
                        ? CrrNeumorphic93.primaryColor
                        : CrrNeumorphic93.cardColor,
                    onTap: () => aiProvider.filterByCategory(category),
                    child: Center(
                      child: Text(
                        category,
                        style: TextStyle(
                          color: isSelected
                              ? Colors.white
                              : CrrNeumorphic93.textPrimary,
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ),
                );
              },
            );
          },
        ),
      ).animate(controller: _headerAnimController)
        .fadeIn(duration: 600.ms, delay: 1000.ms)
        .slideY(begin: 0.3, end: 0),
    );
  }

  /// 构建AI角色卡片网格
  Widget _buildMasterCardsGrid() {
    return Consumer<CrrAiMasterProvider76>(
      builder: (context, aiProvider, child) {
        if (aiProvider.isLoading) {
          return const SliverFillRemaining(
            child: Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF8CC63F)),
              ),
            ),
          );
        }

        final masters = aiProvider.filteredMasters;
        
        return SliverPadding(
          padding: const EdgeInsets.all(20),
          sliver: SliverGrid(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.75,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final master = masters[index];
                return _buildMasterCard(master, index);
              },
              childCount: masters.length,
            ),
          ),
        );
      },
    );
  }

  /// 构建单个AI角色卡片
  Widget _buildMasterCard(CrrAiMaster83 master, int index) {
    return CrrNeumorphic93.card(
      padding: EdgeInsets.zero,
      borderRadius: 20,
      onTap: () => _navigateToChat(master),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // AI角色图片区域
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                boxShadow: CrrNeumorphic93._getInsetShadow(),
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                child: Stack(
                  children: [
                    // 背景图片
                    Positioned.fill(
                      child: Image.asset(
                        master.avatarPath,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: CrrNeumorphic93.primaryColor.withOpacity(0.3),
                            child: Icon(
                              Icons.person,
                              size: 40,
                              color: CrrNeumorphic93.textSecondary,
                            ),
                          );
                        },
                      ),
                    ),

                    // 免费次数标签
                    Positioned(
                      top: 8,
                      right: 8,
                      child: CrrNeumorphic93.container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        borderRadius: 12,
                        backgroundColor: CrrNeumorphic93.primaryColor,
                        child: Text(
                          '${master.freeChatsRemaining} free',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // 信息区域
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // AI角色名称
                  Text(
                    master.name,
                    style: TextStyle(
                      color: CrrNeumorphic93.textPrimary,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 4),

                  // 描述
                  Expanded(
                    child: Text(
                      master.shortDescription,
                      style: TextStyle(
                        color: CrrNeumorphic93.textSecondary,
                        fontSize: 12,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // 分类标签
                  Row(
                    children: [
                      Expanded(
                        child: CrrNeumorphic93.container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          borderRadius: 8,
                          isInset: true,
                          child: Text(
                            master.category,
                            style: TextStyle(
                              color: CrrNeumorphic93.textSecondary,
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      CrrNeumorphic93.container(
                        width: 24,
                        height: 24,
                        borderRadius: 12,
                        child: Icon(
                          Icons.arrow_forward_ios,
                          color: CrrNeumorphic93.textSecondary,
                          size: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    ).animate(controller: _cardsAnimController)
      .fadeIn(duration: 600.ms, delay: (index * 100).ms)
      .slideY(begin: 0.3, end: 0)
      .scale(begin: const Offset(0.8, 0.8), end: const Offset(1, 1));
  }

  /// 导航到聊天页面
  void _navigateToChat(CrrAiMaster83 master) {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            CrrChatWorkshop24(master: master),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }
}
