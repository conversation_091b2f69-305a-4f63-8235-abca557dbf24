import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../ReclaimFlow/CrrAiMasterProvider76.dart';
import '../ReclaimFlow/CrrUserProvider52.dart';
import '../ScrapVault/CrrAiMaster83.dart';
import '../RemakeParts/CrrSoulQuest78.dart';
import 'CrrChatWorkshop24.dart';

/// AI角色选择主页
/// 展示10个AI创意指导师的个性化大图卡片信息流
class CrrAiMaster67Hub extends StatefulWidget {
  const CrrAiMaster67Hub({super.key});

  @override
  State<CrrAiMaster67Hub> createState() => _CrrAiMaster67HubState();
}

class _CrrAiMaster67HubState extends State<CrrAiMaster67Hub>
    with TickerProviderStateMixin {
  late AnimationController _headerAnimController;
  late AnimationController _cardsAnimController;
  final ScrollController _scrollController = ScrollController();
  
  @override
  void initState() {
    super.initState();
    _headerAnimController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _cardsAnimController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _initializeData();
  }

  Future<void> _initializeData() async {
    final aiProvider = context.read<CrrAiMasterProvider76>();
    final userProvider = context.read<CrrUserProvider52>();
    
    await Future.wait([
      aiProvider.initializeMasters(),
      userProvider.initializeUser(),
    ]);
    
    _headerAnimController.forward();
    await Future.delayed(const Duration(milliseconds: 300));
    _cardsAnimController.forward();
  }

  @override
  void dispose() {
    _headerAnimController.dispose();
    _cardsAnimController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF4E1C1), // 自然米色背景
      body: SafeArea(
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            _buildAnimatedHeader(),
            _buildSoulQuestModule(),
            _buildCategoryFilter(),
            _buildMasterCardsGrid(),
          ],
        ),
      ),
    );
  }

  /// 构建动画头部
  Widget _buildAnimatedHeader() {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF8CC63F), Color(0xFF00A9A6)], // 清新绿色到蓝绿色渐变
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Crrie',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          shadows: [
                            Shadow(
                              offset: const Offset(2, 2),
                              blurRadius: 4,
                              color: Colors.black.withOpacity(0.3),
                            ),
                          ],
                        ),
                      ).animate(controller: _headerAnimController)
                        .fadeIn(duration: 600.ms)
                        .slideX(begin: -0.3, end: 0),
                      
                      const SizedBox(height: 8),
                      
                      Text(
                        'Your AI Household Waste Upcycling Guides',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white.withOpacity(0.9),
                          fontWeight: FontWeight.w500,
                        ),
                      ).animate(controller: _headerAnimController)
                        .fadeIn(duration: 600.ms, delay: 200.ms)
                        .slideX(begin: -0.3, end: 0),
                    ],
                  ),
                ),
                
                Consumer<CrrUserProvider52>(
                  builder: (context, userProvider, child) {
                    final user = userProvider.currentUser;
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: const Color(0xFFFF8C00), // 温暖橙色
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.monetization_on,
                            color: Colors.white,
                            size: 18,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${user?.totalCoins ?? 0}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ).animate(controller: _headerAnimController)
                  .fadeIn(duration: 600.ms, delay: 400.ms)
                  .scale(begin: const Offset(0.8, 0.8), end: const Offset(1, 1)),
              ],
            ),
            
            const SizedBox(height: 20),
            
            Consumer<CrrUserProvider52>(
              builder: (context, userProvider, child) {
                final user = userProvider.currentUser;
                return Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.white.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 25,
                        backgroundImage: user?.avatarPath != null
                            ? AssetImage(user!.avatarPath)
                            : null,
                        backgroundColor: Colors.white.withOpacity(0.3),
                        child: user?.avatarPath == null
                            ? const Icon(Icons.person, color: Colors.white)
                            : null,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Welcome back, ${user?.nickname ?? 'Eco Crafter'}!',
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                            Text(
                              user?.userLevelTitle ?? 'Eco Beginner',
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.8),
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: const Color(0xFFFFEB3B).withOpacity(0.9), // 亮黄色
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'Lv.${user?.userLevel ?? 1}',
                          style: const TextStyle(
                            color: Color(0xFF333333),
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ).animate(controller: _headerAnimController)
              .fadeIn(duration: 600.ms, delay: 600.ms)
              .slideY(begin: 0.3, end: 0),
          ],
        ),
      ),
    );
  }

  /// 构建灵魂提问模块
  Widget _buildSoulQuestModule() {
    return SliverToBoxAdapter(
      child: Consumer<CrrUserProvider52>(
        builder: (context, userProvider, child) {
          final user = userProvider.currentUser;
          if (user == null || !user.canAnswerSoulQuestionToday) {
            return const SizedBox.shrink();
          }
          
          return Container(
            margin: const EdgeInsets.all(20),
            child: const CrrSoulQuest78(),
          ).animate(controller: _headerAnimController)
            .fadeIn(duration: 600.ms, delay: 800.ms)
            .slideY(begin: 0.3, end: 0);
        },
      ),
    );
  }

  /// 构建分类筛选器
  Widget _buildCategoryFilter() {
    return SliverToBoxAdapter(
      child: Container(
        height: 50,
        margin: const EdgeInsets.symmetric(horizontal: 20),
        child: Consumer<CrrAiMasterProvider76>(
          builder: (context, aiProvider, child) {
            final categories = aiProvider.getAllCategories();
            
            return ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: categories.length,
              itemBuilder: (context, index) {
                final category = categories[index];
                final isSelected = aiProvider.selectedCategory == category;
                
                return Container(
                  margin: const EdgeInsets.only(right: 12),
                  child: FilterChip(
                    label: Text(
                      category,
                      style: TextStyle(
                        color: isSelected ? Colors.white : const Color(0xFF333333),
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                    selected: isSelected,
                    onSelected: (selected) {
                      aiProvider.filterByCategory(category);
                    },
                    backgroundColor: Colors.white,
                    selectedColor: const Color(0xFF8CC63F),
                    checkmarkColor: Colors.white,
                    elevation: isSelected ? 4 : 2,
                    shadowColor: Colors.black.withOpacity(0.2),
                  ),
                );
              },
            );
          },
        ),
      ).animate(controller: _headerAnimController)
        .fadeIn(duration: 600.ms, delay: 1000.ms)
        .slideY(begin: 0.3, end: 0),
    );
  }

  /// 构建AI角色卡片网格
  Widget _buildMasterCardsGrid() {
    return Consumer<CrrAiMasterProvider76>(
      builder: (context, aiProvider, child) {
        if (aiProvider.isLoading) {
          return const SliverFillRemaining(
            child: Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF8CC63F)),
              ),
            ),
          );
        }

        final masters = aiProvider.filteredMasters;
        
        return SliverPadding(
          padding: const EdgeInsets.all(20),
          sliver: SliverGrid(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.75,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final master = masters[index];
                return _buildMasterCard(master, index);
              },
              childCount: masters.length,
            ),
          ),
        );
      },
    );
  }

  /// 构建单个AI角色卡片
  Widget _buildMasterCard(CrrAiMaster83 master, int index) {
    return GestureDetector(
      onTap: () => _navigateToChat(master),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Stack(
            children: [
              // 背景图片
              Positioned.fill(
                child: Image.asset(
                  master.avatarPath,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: const Color(0xFF8CC63F).withOpacity(0.3),
                      child: const Icon(
                        Icons.person,
                        size: 50,
                        color: Colors.white,
                      ),
                    );
                  },
                ),
              ),
              
              // 渐变遮罩
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.7),
                      ],
                      stops: const [0.4, 1.0],
                    ),
                  ),
                ),
              ),
              
              // 内容
              Positioned(
                left: 12,
                right: 12,
                bottom: 12,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      master.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      master.shortDescription,
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 12,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: const Color(0xFF8CC63F),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            '${master.freeChatsRemaining} free',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        const Spacer(),
                        const Icon(
                          Icons.arrow_forward_ios,
                          color: Colors.white,
                          size: 16,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ).animate(controller: _cardsAnimController)
      .fadeIn(duration: 600.ms, delay: (index * 100).ms)
      .slideY(begin: 0.3, end: 0)
      .scale(begin: const Offset(0.8, 0.8), end: const Offset(1, 1));
  }

  /// 导航到聊天页面
  void _navigateToChat(CrrAiMaster83 master) {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            CrrChatWorkshop24(master: master),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }
}
