import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../ReclaimFlow/CrrUserProvider52.dart';
import '../RemakeParts/CrrNeumorphic93.dart';
import 'CrrCoinManager78.dart';
import 'CrrTreasureVault45.dart';
import 'CrrOpinionCenter92.dart';
import 'CrrFavoriteGallery84.dart';

/// 个人中心页面
/// 展示用户信息、等级、成就、设置等
class CrrProfileCenter89 extends StatefulWidget {
  const CrrProfileCenter89({super.key});

  @override
  State<CrrProfileCenter89> createState() => _CrrProfileCenter89State();
}

class _CrrProfileCenter89State extends State<CrrProfileCenter89>
    with TickerProviderStateMixin {
  late AnimationController _animController;

  @override
  void initState() {
    super.initState();
    _animController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _animController.forward();
  }

  @override
  void dispose() {
    _animController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CrrNeumorphic93.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              _buildHeader(),
              const SizedBox(height: 24),
              _buildUserInfoCard(),
              const SizedBox(height: 20),
              _buildStatsCards(),
              const SizedBox(height: 20),
              _buildMenuItems(),
              const SizedBox(height: 100), // 底部导航栏空间
            ],
          ),
        ),
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Row(
      children: [
        Text(
          'Profile',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: CrrNeumorphic93.textPrimary,
          ),
        ).animate(controller: _animController)
          .fadeIn(duration: 600.ms)
          .slideX(begin: -0.3, end: 0),
        
        const Spacer(),
        
        CrrNeumorphic93.container(
          width: 40,
          height: 40,
          borderRadius: 20,
          child: Icon(
            Icons.settings,
            color: CrrNeumorphic93.textSecondary,
            size: 20,
          ),
        ).animate(controller: _animController)
          .fadeIn(duration: 600.ms, delay: 200.ms)
          .scale(begin: const Offset(0.8, 0.8), end: const Offset(1, 1)),
      ],
    );
  }

  /// 构建用户信息卡片
  Widget _buildUserInfoCard() {
    return Consumer<CrrUserProvider52>(
      builder: (context, userProvider, child) {
        final user = userProvider.currentUser;
        
        return CrrNeumorphic93.card(
          padding: const EdgeInsets.all(24),
          borderRadius: 24,
          child: Column(
            children: [
              // 用户头像
              CrrNeumorphic93.container(
                width: 80,
                height: 80,
                borderRadius: 40,
                isInset: true,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(40),
                  child: user?.avatarPath != null
                      ? Image.asset(
                          user!.avatarPath,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: CrrNeumorphic93.primaryColor.withOpacity(0.3),
                              child: Icon(
                                Icons.person,
                                color: CrrNeumorphic93.textSecondary,
                                size: 40,
                              ),
                            );
                          },
                        )
                      : Container(
                          color: CrrNeumorphic93.primaryColor.withOpacity(0.3),
                          child: Icon(
                            Icons.person,
                            color: CrrNeumorphic93.textSecondary,
                            size: 40,
                          ),
                        ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // 用户名
              Text(
                user?.nickname ?? 'Eco Crafter',
                style: TextStyle(
                  color: CrrNeumorphic93.textPrimary,
                  fontWeight: FontWeight.bold,
                  fontSize: 24,
                ),
              ),
              
              const SizedBox(height: 8),
              
              // 用户等级
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CrrNeumorphic93.container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    borderRadius: 16,
                    backgroundColor: CrrNeumorphic93.primaryColor,
                    child: Text(
                      'Lv.${user?.userLevel ?? 1}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    user?.userLevelTitle ?? 'Eco Beginner',
                    style: TextStyle(
                      color: CrrNeumorphic93.textSecondary,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),

              // 金币显示 - 点击跳转到商城
              Consumer<CrrCoinManager78>(
                builder: (context, coinManager, child) {
                  return GestureDetector(
                    onTap: () => _navigateToTreasureVault(),
                    child: CrrNeumorphic93.container(
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                      borderRadius: 20,
                      backgroundColor: CrrNeumorphic93.accentColor,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.diamond,
                            color: Colors.white,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '${coinManager.formattedBalance} Gems',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 8),
                          const Icon(
                            Icons.add_circle_outline,
                            color: Colors.white,
                            size: 18,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ).animate(controller: _animController)
          .fadeIn(duration: 600.ms, delay: 400.ms)
          .slideY(begin: 0.3, end: 0);
      },
    );
  }

  /// 构建统计卡片
  Widget _buildStatsCards() {
    return Consumer<CrrUserProvider52>(
      builder: (context, userProvider, child) {
        final user = userProvider.currentUser;
        
        return Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Chat Sessions',
                '${user?.totalChatSessions ?? 0}',
                Icons.chat_bubble_rounded,
                CrrNeumorphic93.primaryColor,
                0,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'Favorite Experts',
                '${user?.favoriteCount ?? 0}',
                Icons.favorite,
                Colors.red,
                1,
                onTap: () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const CrrFavoriteGallery84(),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 构建单个统计卡片
  Widget _buildStatCard(String title, String value, IconData icon, Color color, int index, {VoidCallback? onTap}) {
    return CrrNeumorphic93.card(
      padding: const EdgeInsets.all(20),
      borderRadius: 20,
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: color,
              boxShadow: CrrNeumorphic93.getElevatedShadow(),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              color: CrrNeumorphic93.textPrimary,
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              color: CrrNeumorphic93.textSecondary,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ).animate(controller: _animController)
      .fadeIn(duration: 600.ms, delay: (600 + index * 100).ms)
      .slideY(begin: 0.3, end: 0);
  }

  /// 构建菜单项
  Widget _buildMenuItems() {
    final menuItems = [
      MenuItemData('Edit Profile', Icons.edit, () {}),
      MenuItemData('Achievements', Icons.emoji_events, () {}),
      MenuItemData('Chat History', Icons.history, () {}),
      MenuItemData('Opinion Center', Icons.rate_review, () => _navigateToOpinionCenter()),
      MenuItemData('Settings', Icons.settings, () {}),
      MenuItemData('Help & Support', Icons.help, () {}),
      MenuItemData('About', Icons.info, () {}),
    ];

    return Column(
      children: List.generate(
        menuItems.length,
        (index) => Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: _buildMenuItem(menuItems[index], index),
        ),
      ),
    );
  }

  /// 构建单个菜单项
  Widget _buildMenuItem(MenuItemData item, int index) {
    return CrrNeumorphic93.card(
      padding: const EdgeInsets.all(20),
      borderRadius: 16,
      onTap: item.onTap,
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: CrrNeumorphic93.primaryColor.withOpacity(0.1),
            ),
            child: Icon(
              item.icon,
              color: CrrNeumorphic93.primaryColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              item.title,
              style: TextStyle(
                color: CrrNeumorphic93.textPrimary,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            color: CrrNeumorphic93.textSecondary,
            size: 16,
          ),
        ],
      ),
    ).animate(controller: _animController)
      .fadeIn(duration: 600.ms, delay: (800 + index * 100).ms)
      .slideX(begin: 0.3, end: 0);
  }

  /// 导航到金币商城
  void _navigateToTreasureVault() {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const CrrTreasureVault45(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.ease;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }

  /// 导航到意见中心
  void _navigateToOpinionCenter() {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const CrrOpinionCenter92(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.ease;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }
}

/// 菜单项数据模型
class MenuItemData {
  final String title;
  final IconData icon;
  final VoidCallback onTap;

  MenuItemData(this.title, this.icon, this.onTap);
}
