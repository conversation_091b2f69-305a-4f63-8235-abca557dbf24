import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../ReclaimFlow/CrrUserProvider52.dart';
import '../RemakeParts/CrrNeumorphic93.dart';
import '../RemakeParts/CrrAppTitle93.dart';
import 'CrrCoinManager78.dart';
import 'CrrTreasureVault45.dart';
import 'CrrOpinionCenter92.dart';
import 'CrrFavoriteGallery84.dart';
import 'CrrConversationArchive67.dart';
import 'CrrWebViewer34.dart';

/// 个人中心页面
/// 展示用户信息、等级、成就、设置等
class CrrProfileCenter89 extends StatefulWidget {
  const CrrProfileCenter89({super.key});

  @override
  State<CrrProfileCenter89> createState() => _CrrProfileCenter89State();
}

class _CrrProfileCenter89State extends State<CrrProfileCenter89>
    with TickerProviderStateMixin {
  late AnimationController _animController;

  @override
  void initState() {
    super.initState();
    _animController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _animController.forward();
  }

  @override
  void dispose() {
    _animController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CrrNeumorphic93.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              _buildHeader(),
              const SizedBox(height: 24),
              _buildUserInfoCard(),
              const SizedBox(height: 20),
              _buildStatsCards(),
              const SizedBox(height: 20),
              _buildMenuItems(),
              const SizedBox(height: 100), // 底部导航栏空间
            ],
          ),
        ),
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Row(
      children: [
        CrrAppTitle93(
          title: 'Profile',
          animationController: _animController,
          fontSize: 32,
        ),
        
        // const Spacer(),
        //
        // CrrNeumorphic93.container(
        //   width: 40,
        //   height: 40,
        //   borderRadius: 20,
        //   child: Icon(
        //     Icons.settings,
        //     color: CrrNeumorphic93.textSecondary,
        //     size: 20,
        //   ),
        // ).animate(controller: _animController)
        //   .fadeIn(duration: 600.ms, delay: 200.ms)
        //   .scale(begin: const Offset(0.8, 0.8), end: const Offset(1, 1)),
      ],
    );
  }

  /// 构建用户信息卡片
  Widget _buildUserInfoCard() {
    return Consumer<CrrUserProvider52>(
      builder: (context, userProvider, child) {
        final user = userProvider.currentUser;

        return CrrNeumorphic93.card(
          padding: const EdgeInsets.all(20),
          borderRadius: 24,
          child: Row(
            children: [
              // 左侧头像
              GestureDetector(
                onTap: _showAvatarPicker,
                child: Container(
                  width: 70,
                  height: 70,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: CrrNeumorphic93.getElevatedShadow(),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(35),
                    child: Image.asset(
                      user?.avatarPath ?? 'assets/avatars/avatar_01.jpg',
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: CrrNeumorphic93.primaryColor.withOpacity(0.3),
                          child: const Icon(
                            Icons.person,
                            color: CrrNeumorphic93.textSecondary,
                            size: 35,
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // 右侧信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 用户名和编辑按钮
                    Row(
                      children: [
                        Expanded(
                          child: GestureDetector(
                            onTap: _showNicknameEditor,
                            child: Text(
                              user?.nickname ?? 'Eco Crafter',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: CrrNeumorphic93.textPrimary,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                        CrrNeumorphic93.container(
                          width: 32,
                          height: 32,
                          borderRadius: 16,
                          backgroundColor: CrrNeumorphic93.cardColor,
                          onTap: _showNicknameEditor,
                          child: const Icon(
                            Icons.edit,
                            color: CrrNeumorphic93.textSecondary,
                            size: 16,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 12),

                    // 金币显示
                    Consumer<CrrCoinManager78>(
                      builder: (context, coinManager, child) {
                        return GestureDetector(
                          onTap: () => _navigateToTreasureVault(),
                          child: Row(
                            children: [
                              const Icon(
                                Icons.diamond,
                                color: Colors.amber,
                                size: 18,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                '${coinManager.formattedBalance}',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: CrrNeumorphic93.textPrimary,
                                ),
                              ),
                              const SizedBox(width: 4),
                              const Text(
                                'Gems',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: CrrNeumorphic93.textSecondary,
                                ),
                              ),
                              const SizedBox(width: 8),
                              const Icon(
                                Icons.add_circle_outline,
                                color: CrrNeumorphic93.textSecondary,
                                size: 16,
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ).animate(controller: _animController)
          .fadeIn(duration: 600.ms, delay: 400.ms)
          .slideY(begin: 0.3, end: 0);
      },
    );
  }

  /// 构建统计卡片
  Widget _buildStatsCards() {
    return Consumer<CrrUserProvider52>(
      builder: (context, userProvider, child) {
        final user = userProvider.currentUser;
        
        return Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Chat Sessions',
                '${user?.totalChatSessions ?? 0}',
                Icons.chat_bubble_rounded,
                CrrNeumorphic93.primaryColor,
                0,
                onTap: () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const CrrConversationArchive67(showBackButton: true),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'Favorite Experts',
                '${user?.favoriteCount ?? 0}',
                Icons.favorite,
                Colors.red,
                1,
                onTap: () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const CrrFavoriteGallery84(),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 构建单个统计卡片
  Widget _buildStatCard(String title, String value, IconData icon, Color color, int index, {VoidCallback? onTap}) {
    return CrrNeumorphic93.card(
      padding: const EdgeInsets.all(20),
      borderRadius: 20,
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: color,
              boxShadow: CrrNeumorphic93.getElevatedShadow(),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              color: CrrNeumorphic93.textPrimary,
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              color: CrrNeumorphic93.textSecondary,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ).animate(controller: _animController)
      .fadeIn(duration: 600.ms, delay: (600 + index * 100).ms)
      .slideY(begin: 0.3, end: 0);
  }

  /// 构建菜单项
  Widget _buildMenuItems() {
    final menuItems = [
      // MenuItemData('Edit Profile', Icons.edit, () {}),
      // MenuItemData('Achievements', Icons.emoji_events, () {}),
      // MenuItemData('Chat History', Icons.history, () {}),
      MenuItemData('Opinion Center', Icons.rate_review, () => _navigateToOpinionCenter()),
      MenuItemData('Privacy Policy', Icons.privacy_tip, () => _navigateToPrivacyPolicy()),
      // MenuItemData('Settings', Icons.settings, () {}),
      // MenuItemData('Help & Support', Icons.help, () {}),
      MenuItemData('About', Icons.info, () {}),
    ];

    return Column(
      children: List.generate(
        menuItems.length,
        (index) => Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: _buildMenuItem(menuItems[index], index),
        ),
      ),
    );
  }

  /// 构建单个菜单项
  Widget _buildMenuItem(MenuItemData item, int index) {
    return CrrNeumorphic93.card(
      padding: const EdgeInsets.all(20),
      borderRadius: 16,
      onTap: item.onTap,
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: CrrNeumorphic93.primaryColor.withOpacity(0.1),
            ),
            child: Icon(
              item.icon,
              color: CrrNeumorphic93.primaryColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              item.title,
              style: TextStyle(
                color: CrrNeumorphic93.textPrimary,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            color: CrrNeumorphic93.textSecondary,
            size: 16,
          ),
        ],
      ),
    ).animate(controller: _animController)
      .fadeIn(duration: 600.ms, delay: (800 + index * 100).ms)
      .slideX(begin: 0.3, end: 0);
  }

  /// 导航到宝石商城
  void _navigateToTreasureVault() async {
    final result = await Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const CrrTreasureVault45(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.ease;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );

    // 从宝石商城返回后，刷新状态
    if (result == true && mounted) {
      setState(() {
        // 触发重建以更新宝石余额显示
      });
    }
  }

  /// 导航到意见中心
  void _navigateToOpinionCenter() {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const CrrOpinionCenter92(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.ease;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }

  /// 显示头像选择器
  void _showAvatarPicker() {
    final List<String> avatarPaths = [
      'assets/avatars/avatar_01.jpg',
      'assets/avatars/avatar_02.jpg',
      'assets/avatars/avatar_03.jpg',
      'assets/avatars/avatar_04.jpg',
      'assets/avatars/avatar_05.jpg',
      'assets/avatars/avatar_06.jpg',
      'assets/avatars/avatar_07.jpg',
      'assets/avatars/avatar_08.jpg',
    ];

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: CrrNeumorphic93.backgroundColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            const Text(
              'Choose Avatar',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: CrrNeumorphic93.textPrimary,
              ),
            ),
            const SizedBox(height: 20),

            // 头像网格
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: avatarPaths.length,
              itemBuilder: (context, index) {
                final avatarPath = avatarPaths[index];
                final isSelected = Provider.of<CrrUserProvider52>(context, listen: false)
                    .currentUser?.avatarPath == avatarPath;

                return GestureDetector(
                  onTap: () async {
                    await Provider.of<CrrUserProvider52>(context, listen: false)
                        .updateAvatar(avatarPath);
                    Navigator.pop(context);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: isSelected
                          ? Border.all(color: CrrNeumorphic93.primaryColor, width: 3)
                          : null,
                      boxShadow: CrrNeumorphic93.getElevatedShadow(),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(40),
                      child: Image.asset(
                        avatarPath,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// 显示昵称编辑器
  void _showNicknameEditor() {
    final TextEditingController controller = TextEditingController();
    final currentNickname = Provider.of<CrrUserProvider52>(context, listen: false)
        .currentUser?.nickname ?? 'Eco Crafter';
    controller.text = currentNickname;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: CrrNeumorphic93.backgroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text(
          'Edit Nickname',
          style: TextStyle(
            color: CrrNeumorphic93.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: TextField(
          controller: controller,
          maxLength: 20,
          decoration: InputDecoration(
            hintText: 'Enter your nickname',
            hintStyle: const TextStyle(color: CrrNeumorphic93.textSecondary),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: CrrNeumorphic93.primaryColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: CrrNeumorphic93.primaryColor, width: 2),
            ),
          ),
          style: const TextStyle(color: CrrNeumorphic93.textPrimary),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: CrrNeumorphic93.textSecondary),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              final newNickname = controller.text.trim();
              if (newNickname.isNotEmpty && newNickname != currentNickname) {
                await Provider.of<CrrUserProvider52>(context, listen: false)
                    .updateNickname(newNickname);
              }
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: CrrNeumorphic93.primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'Save',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  /// 导航到隐私协议页面
  void _navigateToPrivacyPolicy() {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const CrrWebViewer34(
              url: 'https://docs.google.com/document/d/1qqOgiD74LGxvL4dhn7mgcWnTFm4YSnZvnFzzdaCUs3g/edit?usp=sharing',
              title: 'Privacy Policy',
            ),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.ease;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }
}

/// 菜单项数据模型
class MenuItemData {
  final String title;
  final IconData icon;
  final VoidCallback onTap;

  MenuItemData(this.title, this.icon, this.onTap);
}
