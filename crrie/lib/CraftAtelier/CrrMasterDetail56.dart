import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../ScrapVault/CrrAiMaster83.dart';
import '../ReclaimFlow/CrrUserProvider52.dart';
import '../RemakeParts/CrrNeumorphic93.dart';
import 'CrrChatWorkshop24.dart';

/// AI角色详情页面
/// 展示角色的详细信息、专长、示例对话等
class CrrMasterDetail56 extends StatefulWidget {
  final CrrAiMaster83 master;

  const CrrMasterDetail56({
    super.key,
    required this.master,
  });

  @override
  State<CrrMasterDetail56> createState() => _CrrMasterDetail56State();
}

class _CrrMasterDetail56State extends State<CrrMasterDetail56>
    with TickerProviderStateMixin {
  late AnimationController _animController;

  @override
  void initState() {
    super.initState();
    _animController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _animController.forward();
  }

  @override
  void dispose() {
    _animController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CrrNeumorphic93.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              _buildHeader(),
              _buildMasterImage(),
              _buildMasterInfo(),
              _buildSpecialties(),
              _buildExampleQuestions(),
              _buildActionButtons(),
              const SizedBox(height: 100), // 底部空间
            ],
          ),
        ),
      ),
    );
  }

  /// 构建头部导航
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          CrrNeumorphic93.container(
            width: 40,
            height: 40,
            borderRadius: 20,
            onTap: () => Navigator.pop(context),
            child: Icon(
              Icons.arrow_back_ios_new,
              color: CrrNeumorphic93.textSecondary,
              size: 20,
            ),
          ),
          
          const SizedBox(width: 16),
          
          Expanded(
            child: Text(
              'AI Master Details',
              style: TextStyle(
                color: CrrNeumorphic93.textPrimary,
                fontWeight: FontWeight.bold,
                fontSize: 20,
              ),
            ),
          ),
          
          // 收藏按钮
          Consumer<CrrUserProvider52>(
            builder: (context, userProvider, child) {
              final user = userProvider.currentUser;
              final isFavorited = user?.isMasterFavorited(widget.master.masterId) ?? false;
              
              return CrrNeumorphic93.container(
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: isFavorited 
                    ? Colors.red.withOpacity(0.1)
                    : CrrNeumorphic93.cardColor,
                onTap: _toggleFavorite,
                child: Icon(
                  isFavorited ? Icons.favorite : Icons.favorite_border,
                  color: isFavorited ? Colors.red : CrrNeumorphic93.textSecondary,
                  size: 20,
                ),
              );
            },
          ),
        ],
      ),
    ).animate(controller: _animController)
      .fadeIn(duration: 600.ms)
      .slideY(begin: -0.3, end: 0);
  }

  /// 构建角色图片
  Widget _buildMasterImage() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: CrrNeumorphic93.card(
        padding: EdgeInsets.zero,
        borderRadius: 24,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(24),
          child: Column(
            children: [
              // 图片容器 - 适配垂直图片
              Container(
                width: double.infinity,
                constraints: const BoxConstraints(
                  maxHeight: 400, // 增加最大高度
                  minHeight: 300, // 设置最小高度
                ),
                child: Stack(
                  children: [
                    // 背景图片
                    Container(
                      width: double.infinity,
                      child: Image.asset(
                        widget.master.avatarPath,
                        fit: BoxFit.contain, // 改为contain以显示完整图片
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            height: 300,
                            color: CrrNeumorphic93.primaryColor.withOpacity(0.3),
                            child: Icon(
                              Icons.person,
                              size: 80,
                              color: CrrNeumorphic93.textSecondary,
                            ),
                          );
                        },
                      ),
                    ),

                    // 渐变遮罩 - 仅在底部
                    Positioned(
                      left: 0,
                      right: 0,
                      bottom: 0,
                      height: 100,
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Colors.black.withOpacity(0.7),
                            ],
                          ),
                        ),
                      ),
                    ),

                    // 角色名称
                    Positioned(
                      left: 20,
                      right: 20,
                      bottom: 20,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                      Text(
                        widget.master.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 24,
                        ),
                      ),
                      const SizedBox(height: 4),
                      CrrNeumorphic93.container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        borderRadius: 12,
                        backgroundColor: CrrNeumorphic93.primaryColor,
                        child: Text(
                          widget.master.category,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                        ],
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
        ),
      ),
    ).animate(controller: _animController)
      .fadeIn(duration: 600.ms, delay: 200.ms)
      .slideY(begin: 0.3, end: 0);
  }

  /// 构建角色信息
  Widget _buildMasterInfo() {
    return Container(
      margin: const EdgeInsets.all(20),
      child: CrrNeumorphic93.card(
        padding: const EdgeInsets.all(24),
        borderRadius: 20,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'About ${widget.master.name}',
              style: TextStyle(
                color: CrrNeumorphic93.textPrimary,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              widget.master.detailedDescription,
              style: TextStyle(
                color: CrrNeumorphic93.textSecondary,
                fontSize: 16,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    ).animate(controller: _animController)
      .fadeIn(duration: 600.ms, delay: 400.ms)
      .slideY(begin: 0.3, end: 0);
  }

  /// 构建专长领域
  Widget _buildSpecialties() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: CrrNeumorphic93.card(
        padding: const EdgeInsets.all(24),
        borderRadius: 20,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Specialties',
              style: TextStyle(
                color: CrrNeumorphic93.textPrimary,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: widget.master.specialties.map((specialty) {
                return CrrNeumorphic93.container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  borderRadius: 12,
                  isInset: true,
                  child: Text(
                    specialty,
                    style: TextStyle(
                      color: CrrNeumorphic93.textSecondary,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    ).animate(controller: _animController)
      .fadeIn(duration: 600.ms, delay: 600.ms)
      .slideY(begin: 0.3, end: 0);
  }

  /// 构建示例问题
  Widget _buildExampleQuestions() {
    return Container(
      margin: const EdgeInsets.all(20),
      child: CrrNeumorphic93.card(
        padding: const EdgeInsets.all(24),
        borderRadius: 20,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Try asking me...',
              style: TextStyle(
                color: CrrNeumorphic93.textPrimary,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 16),
            ...widget.master.exampleQuestions.map((question) {
              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                child: CrrNeumorphic93.container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  borderRadius: 12,
                  isInset: true,
                  child: Row(
                    children: [
                      Icon(
                        Icons.chat_bubble_outline,
                        color: CrrNeumorphic93.primaryColor,
                        size: 16,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          question,
                          style: TextStyle(
                            color: CrrNeumorphic93.textSecondary,
                            fontSize: 14,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ],
        ),
      ),
    ).animate(controller: _animController)
      .fadeIn(duration: 600.ms, delay: 800.ms)
      .slideY(begin: 0.3, end: 0);
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: CrrNeumorphic93.button(
        onPressed: _startChat,
        height: 56,
        borderRadius: 20,
        backgroundColor: CrrNeumorphic93.primaryColor,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.chat_bubble_rounded,
              color: Colors.white,
              size: 24,
            ),
            const SizedBox(width: 12),
            const Text(
              'Start Chatting',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
          ],
        ),
      ),
    ).animate(controller: _animController)
      .fadeIn(duration: 600.ms, delay: 1000.ms)
      .slideY(begin: 0.3, end: 0);
  }

  /// 开始聊天
  void _startChat() {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            CrrChatWorkshop24(master: widget.master),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.ease;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }

  /// 切换收藏状态
  void _toggleFavorite() {
    final userProvider = context.read<CrrUserProvider52>();
    userProvider.toggleMasterFavorite(widget.master.masterId);
    
    // 显示反馈
    final user = userProvider.currentUser;
    final isFavorited = user?.isMasterFavorited(widget.master.masterId) ?? false;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isFavorited ? 'Added to favorites ❤️' : 'Removed from favorites',
        ),
        backgroundColor: CrrNeumorphic93.primaryColor,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
