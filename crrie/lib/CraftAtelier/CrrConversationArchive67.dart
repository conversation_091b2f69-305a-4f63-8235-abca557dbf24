import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../ScrapVault/CrrChatModel29.dart';
import '../ScrapVault/CrrAiMaster83.dart';
import '../UpcycleMaster/CrrConversationService84.dart';
import '../RemakeParts/CrrNeumorphic93.dart';
import 'CrrChatWorkshop24.dart';

/// 聊天历史记录管理页面
/// 高级UI设计，展示所有聊天会话记录
class CrrConversationArchive67 extends StatefulWidget {
  const CrrConversationArchive67({super.key});

  @override
  State<CrrConversationArchive67> createState() => _CrrConversationArchive67State();
}

class _CrrConversationArchive67State extends State<CrrConversationArchive67>
    with TickerProviderStateMixin {
  final CrrConversationService84 _conversationService = CrrConversationService84();
  List<CrrChatSession56> _sessions = [];
  bool _isLoading = true;
  String _searchQuery = '';
  
  late AnimationController _headerAnimController;
  late AnimationController _listAnimController;
  
  @override
  void initState() {
    super.initState();
    _headerAnimController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _listAnimController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _loadConversationSessions();
  }
  
  @override
  void dispose() {
    _headerAnimController.dispose();
    _listAnimController.dispose();
    super.dispose();
  }
  
  /// 加载聊天会话列表
  Future<void> _loadConversationSessions() async {
    setState(() => _isLoading = true);
    
    try {
      final sessions = await _conversationService.getAllConversationSessions();
      setState(() {
        _sessions = sessions;
        _isLoading = false;
      });
      
      _headerAnimController.forward();
      _listAnimController.forward();
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorMessage('加载聊天记录失败');
    }
  }
  
  /// 过滤会话列表
  List<CrrChatSession56> get _filteredSessions {
    if (_searchQuery.isEmpty) return _sessions;
    
    return _sessions.where((session) {
      return session.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             session.lastMessagePreview.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CrrNeumorphic93.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            _buildAnimatedHeader(),
            _buildSearchBar(),
            Expanded(child: _buildConversationList()),
          ],
        ),
      ),
    );
  }
  
  /// 构建动画头部
  Widget _buildAnimatedHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // 返回按钮
          CrrNeumorphic93.container(
            width: 48,
            height: 48,
            borderRadius: 24,
            onTap: () => Navigator.pop(context),
            child: const Icon(
              Icons.arrow_back_ios_new,
              color: CrrNeumorphic93.textPrimary,
              size: 20,
            ),
          ).animate(controller: _headerAnimController)
            .slideX(begin: -1, end: 0, duration: 600.ms)
            .fadeIn(duration: 600.ms),
          
          const SizedBox(width: 16),
          
          // 标题
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Conversation Archive',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: CrrNeumorphic93.textPrimary,
                  ),
                ).animate(controller: _headerAnimController)
                  .slideY(begin: -0.5, end: 0, duration: 600.ms, delay: 200.ms)
                  .fadeIn(duration: 600.ms, delay: 200.ms),
                
                const SizedBox(height: 4),
                
                Text(
                  '${_sessions.length} conversations',
                  style: const TextStyle(
                    fontSize: 14,
                    color: CrrNeumorphic93.textSecondary,
                  ),
                ).animate(controller: _headerAnimController)
                  .slideY(begin: 0.5, end: 0, duration: 600.ms, delay: 400.ms)
                  .fadeIn(duration: 600.ms, delay: 400.ms),
              ],
            ),
          ),
          
          // 清空按钮
          CrrNeumorphic93.container(
            width: 48,
            height: 48,
            borderRadius: 24,
            backgroundColor: CrrNeumorphic93.dangerColor,
            onTap: _showClearAllDialog,
            child: const Icon(
              Icons.delete_sweep,
              color: Colors.white,
              size: 20,
            ),
          ).animate(controller: _headerAnimController)
            .slideX(begin: 1, end: 0, duration: 600.ms, delay: 300.ms)
            .fadeIn(duration: 600.ms, delay: 300.ms),
        ],
      ),
    );
  }
  
  /// 构建搜索栏
  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: CrrNeumorphic93.textField(
        hintText: 'Search conversations...',
        prefixIcon: Icons.search,
        borderRadius: 20,
        onChanged: (value) {
          setState(() => _searchQuery = value);
        },
      ),
    ).animate(controller: _headerAnimController)
      .slideY(begin: 0.5, end: 0, duration: 600.ms, delay: 600.ms)
      .fadeIn(duration: 600.ms, delay: 600.ms);
  }
  
  /// 构建对话列表
  Widget _buildConversationList() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(CrrNeumorphic93.primaryColor),
        ),
      );
    }
    
    final filteredSessions = _filteredSessions;
    
    if (filteredSessions.isEmpty) {
      return _buildEmptyState();
    }
    
    return Container(
      margin: const EdgeInsets.all(20),
      child: ListView.builder(
        itemCount: filteredSessions.length,
        itemBuilder: (context, index) {
          final session = filteredSessions[index];
          return _buildConversationCard(session, index);
        },
      ),
    );
  }
  
  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 80,
            color: CrrNeumorphic93.textSecondary.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isEmpty ? 'No conversations yet' : 'No matching conversations',
            style: const TextStyle(
              fontSize: 18,
              color: CrrNeumorphic93.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isEmpty 
                ? 'Start chatting with AI masters to see your conversations here'
                : 'Try a different search term',
            style: const TextStyle(
              fontSize: 14,
              color: CrrNeumorphic93.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ).animate(controller: _listAnimController)
      .fadeIn(duration: 800.ms)
      .slideY(begin: 0.3, end: 0, duration: 800.ms);
  }
  
  /// 构建对话卡片
  Widget _buildConversationCard(CrrChatSession56 session, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: CrrNeumorphic93.container(
        padding: const EdgeInsets.all(16),
        borderRadius: 20,
        onTap: () => _openConversation(session),
        child: Row(
          children: [
            // AI角色头像
            _buildMasterAvatar(session.masterId),
            
            const SizedBox(width: 16),
            
            // 对话信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题和时间
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          session.title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: CrrNeumorphic93.textPrimary,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Text(
                        _formatTime(session.lastMessageAt),
                        style: const TextStyle(
                          fontSize: 12,
                          color: CrrNeumorphic93.textSecondary,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // 最后消息预览
                  Text(
                    session.lastMessagePreview,
                    style: const TextStyle(
                      fontSize: 14,
                      color: CrrNeumorphic93.textSecondary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // 消息数量
                  Row(
                    children: [
                      const Icon(
                        Icons.chat_bubble_outline,
                        size: 14,
                        color: CrrNeumorphic93.textSecondary,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${session.messageCount} messages',
                        style: const TextStyle(
                          fontSize: 12,
                          color: CrrNeumorphic93.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // 删除按钮
            CrrNeumorphic93.container(
              width: 36,
              height: 36,
              borderRadius: 18,
              backgroundColor: CrrNeumorphic93.dangerColor.withOpacity(0.1),
              onTap: () => _showDeleteDialog(session),
              child: const Icon(
                Icons.delete_outline,
                color: CrrNeumorphic93.dangerColor,
                size: 18,
              ),
            ),
          ],
        ),
      ),
    ).animate(controller: _listAnimController)
      .slideX(
        begin: 1,
        end: 0,
        duration: 600.ms,
        delay: (index * 100).ms,
      )
      .fadeIn(
        duration: 600.ms,
        delay: (index * 100).ms,
      );
  }

  /// 构建AI角色头像
  Widget _buildMasterAvatar(String masterId) {
    // 根据masterId获取对应的AI角色头像
    final master = CrrAiMasterRepo92.getAllMasters()
        .firstWhere((m) => m.masterId == masterId,
                   orElse: () => CrrAiMasterRepo92.getAllMasters().first);

    return CrrNeumorphic93.container(
      width: 56,
      height: 56,
      borderRadius: 28,
      padding: const EdgeInsets.all(2),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(26),
        child: Image.asset(
          master.avatarPath,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: CrrNeumorphic93.primaryColor.withOpacity(0.3),
              child: const Icon(
                Icons.person,
                color: CrrNeumorphic93.textSecondary,
                size: 28,
              ),
            );
          },
        ),
      ),
    );
  }

  /// 格式化时间显示
  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  /// 打开对话页面
  void _openConversation(CrrChatSession56 session) async {
    // 获取AI角色信息
    final master = CrrAiMasterRepo92.getAllMasters()
        .firstWhere((m) => m.masterId == session.masterId,
                   orElse: () => CrrAiMasterRepo92.getAllMasters().first);

    // 导航到聊天页面
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CrrChatWorkshop24(
          master: master,
          existingSessionId: session.sessionId,
        ),
      ),
    );

    // 如果有更新，重新加载列表
    if (result == true) {
      _loadConversationSessions();
    }
  }

  /// 显示删除对话确认弹窗
  void _showDeleteDialog(CrrChatSession56 session) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: CrrNeumorphic93.backgroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text(
          'Delete Conversation',
          style: TextStyle(
            color: CrrNeumorphic93.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'Are you sure you want to delete this conversation? This action cannot be undone.',
          style: const TextStyle(
            color: CrrNeumorphic93.textSecondary,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: CrrNeumorphic93.textSecondary),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteConversation(session.sessionId);
            },
            child: const Text(
              'Delete',
              style: TextStyle(color: CrrNeumorphic93.dangerColor),
            ),
          ),
        ],
      ),
    );
  }

  /// 显示清空所有对话确认弹窗
  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: CrrNeumorphic93.backgroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text(
          'Clear All Conversations',
          style: TextStyle(
            color: CrrNeumorphic93.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'Are you sure you want to delete all conversations? This action cannot be undone.',
          style: const TextStyle(
            color: CrrNeumorphic93.textSecondary,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: CrrNeumorphic93.textSecondary),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _clearAllConversations();
            },
            child: const Text(
              'Clear All',
              style: TextStyle(color: CrrNeumorphic93.dangerColor),
            ),
          ),
        ],
      ),
    );
  }

  /// 删除单个对话
  Future<void> _deleteConversation(String sessionId) async {
    try {
      await _conversationService.deleteConversationSession(sessionId);
      _loadConversationSessions();
      _showSuccessMessage('Conversation deleted');
    } catch (e) {
      _showErrorMessage('Failed to delete conversation');
    }
  }

  /// 清空所有对话
  Future<void> _clearAllConversations() async {
    try {
      await _conversationService.clearAllConversations();
      _loadConversationSessions();
      _showSuccessMessage('All conversations cleared');
    } catch (e) {
      _showErrorMessage('Failed to clear conversations');
    }
  }

  /// 显示成功消息
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: CrrNeumorphic93.successColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  /// 显示错误消息
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: CrrNeumorphic93.dangerColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}
