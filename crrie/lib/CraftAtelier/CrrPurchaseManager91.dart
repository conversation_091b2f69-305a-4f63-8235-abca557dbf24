import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'CrrSCGoods.dart';
import 'CrrCoinManager78.dart';

/// 内购管理器 - 处理iOS内购逻辑
class CrrPurchaseManager91 {
  static CrrPurchaseManager91? _instance;
  static CrrPurchaseManager91 get instance {
    _instance ??= CrrPurchaseManager91._internal();
    return _instance!;
  }

  CrrPurchaseManager91._internal();

  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  late StreamSubscription<List<PurchaseDetails>> _subscription;
  
  bool _isInitialized = false;
  bool _isPurchasing = false;
  
  /// 购买状态回调
  Function(bool isLoading)? onLoadingChanged;
  Function(String message)? onSuccess;
  Function(String message)? onError;
  Function(String message)? onCancelled;

  /// 初始化内购
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      final bool available = await _inAppPurchase.isAvailable();
      if (!available) {
        if (kDebugMode) {
          print('CrrPurchaseManager91: In-app purchase not available');
        }
        return false;
      }

      // 监听购买状态变化
      _subscription = _inAppPurchase.purchaseStream.listen(
        _handlePurchaseUpdates,
        onDone: () {
          if (kDebugMode) {
            print('CrrPurchaseManager91: Purchase stream closed');
          }
        },
        onError: (error) {
          if (kDebugMode) {
            print('CrrPurchaseManager91: Purchase stream error: $error');
          }
        },
      );

      _isInitialized = true;
      
      if (kDebugMode) {
        print('CrrPurchaseManager91: Initialized successfully');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('CrrPurchaseManager91: Initialization error: $e');
      }
      return false;
    }
  }

  /// 购买商品
  Future<void> purchaseProduct(String productId) async {
    if (_isPurchasing) {
      onError?.call('Another purchase is in progress');
      return;
    }

    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) {
        onError?.call('Purchase service unavailable');
        return;
      }
    }

    // 检查商品是否存在
    final goods = CrrSCGoodsRepository.getGoodsByCode(productId);
    if (goods == null) {
      onError?.call('Product not found');
      return;
    }

    try {
      _isPurchasing = true;
      onLoadingChanged?.call(true);

      // 查询商品信息
      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails({productId});
      
      if (response.error != null) {
        throw Exception('Failed to query product: ${response.error!.message}');
      }

      if (response.productDetails.isEmpty) {
        throw Exception('Product not available in App Store');
      }

      final ProductDetails productDetails = response.productDetails.first;
      
      // 创建购买参数
      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: productDetails,
      );

      // 发起购买
      final bool success = await _inAppPurchase.buyConsumable(
        purchaseParam: purchaseParam,
      );

      if (!success) {
        throw Exception('Failed to initiate purchase');
      }

      if (kDebugMode) {
        print('CrrPurchaseManager91: Purchase initiated for $productId');
      }

    } catch (e) {
      _isPurchasing = false;
      onLoadingChanged?.call(false);
      
      if (kDebugMode) {
        print('CrrPurchaseManager91: Purchase error: $e');
      }
      
      onError?.call('Purchase failed: ${e.toString()}');
    }
  }

  /// 处理购买状态更新
  void _handlePurchaseUpdates(List<PurchaseDetails> purchaseDetailsList) {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      _processPurchase(purchaseDetails);
    }
  }

  /// 处理单个购买
  Future<void> _processPurchase(PurchaseDetails purchaseDetails) async {
    try {
      if (kDebugMode) {
        print('CrrPurchaseManager91: Processing purchase: ${purchaseDetails.productID}, status: ${purchaseDetails.status}');
      }

      switch (purchaseDetails.status) {
        case PurchaseStatus.pending:
          // 等待用户确认，保持加载状态
          if (kDebugMode) {
            print('CrrPurchaseManager91: Purchase pending for ${purchaseDetails.productID}');
          }
          break;

        case PurchaseStatus.purchased:
          // 购买成功
          await _handleSuccessfulPurchase(purchaseDetails);
          break;

        case PurchaseStatus.error:
          // 购买失败
          await _handleFailedPurchase(purchaseDetails);
          break;

        case PurchaseStatus.canceled:
          // 用户取消
          await _handleCancelledPurchase(purchaseDetails);
          break;

        case PurchaseStatus.restored:
          // 恢复购买（对于消耗品通常不会发生）
          if (kDebugMode) {
            print('CrrPurchaseManager91: Purchase restored for ${purchaseDetails.productID}');
          }
          await _completePurchase(purchaseDetails);
          break;
      }
    } catch (e) {
      if (kDebugMode) {
        print('CrrPurchaseManager91: Error processing purchase: $e');
      }
      
      _isPurchasing = false;
      onLoadingChanged?.call(false);
      onError?.call('Failed to process purchase');
      
      // 确保完成交易
      await _completePurchase(purchaseDetails);
    }
  }

  /// 处理成功购买
  Future<void> _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) async {
    try {
      // 获取商品信息
      final goods = CrrSCGoodsRepository.getGoodsByCode(purchaseDetails.productID);
      if (goods == null) {
        throw Exception('Product not found: ${purchaseDetails.productID}');
      }

      // 发放金币
      final coinManager = CrrCoinManager78.instance;
      final success = await coinManager.addCoins(goods.coinAmount);
      
      if (!success) {
        throw Exception('Failed to add coins');
      }

      _isPurchasing = false;
      onLoadingChanged?.call(false);
      onSuccess?.call('Purchase successful! ${goods.formattedCoin} added to your account.');

      if (kDebugMode) {
        print('CrrPurchaseManager91: Successfully added ${goods.coinAmount} coins');
      }

    } catch (e) {
      if (kDebugMode) {
        print('CrrPurchaseManager91: Error handling successful purchase: $e');
      }
      
      _isPurchasing = false;
      onLoadingChanged?.call(false);
      onError?.call('Purchase completed but failed to add coins. Please contact support.');
    } finally {
      // 完成交易
      await _completePurchase(purchaseDetails);
    }
  }

  /// 处理失败购买
  Future<void> _handleFailedPurchase(PurchaseDetails purchaseDetails) async {
    _isPurchasing = false;
    onLoadingChanged?.call(false);

    String errorMessage = 'Purchase failed';
    
    if (purchaseDetails.error != null) {
      final error = purchaseDetails.error!;
      
      // 检查是否是用户取消
      if (error.code == 'storekit2_purchase_cancelled' || 
          error.message.toLowerCase().contains('cancelled by the user')) {
        onCancelled?.call('Purchase cancelled by user');
        if (kDebugMode) {
          print('CrrPurchaseManager91: Purchase cancelled by user: ${purchaseDetails.productID}');
        }
      } else {
        errorMessage = 'Purchase failed: ${error.message}';
        onError?.call(errorMessage);
        if (kDebugMode) {
          print('CrrPurchaseManager91: Purchase failed: ${error.message}');
        }
      }
    } else {
      onError?.call(errorMessage);
    }

    // 完成交易
    await _completePurchase(purchaseDetails);
  }

  /// 处理取消购买
  Future<void> _handleCancelledPurchase(PurchaseDetails purchaseDetails) async {
    _isPurchasing = false;
    onLoadingChanged?.call(false);
    onCancelled?.call('Purchase cancelled by user');

    if (kDebugMode) {
      print('CrrPurchaseManager91: Purchase cancelled: ${purchaseDetails.productID}');
    }

    // 完成交易
    await _completePurchase(purchaseDetails);
  }

  /// 完成交易
  Future<void> _completePurchase(PurchaseDetails purchaseDetails) async {
    try {
      if (purchaseDetails.pendingCompletePurchase) {
        await _inAppPurchase.completePurchase(purchaseDetails);
        if (kDebugMode) {
          print('CrrPurchaseManager91: Completed purchase: ${purchaseDetails.productID}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('CrrPurchaseManager91: Error completing purchase: $e');
      }
    }
  }

  /// 清理资源
  void dispose() {
    _subscription.cancel();
    _isInitialized = false;
    _isPurchasing = false;
  }
}
