import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../RemakeParts/CrrNeumorphic93.dart';
import '../ScrapVault/CrrAgreementTexts92.dart';

/// 用户协议和隐私协议弹窗
/// 在用户首次使用应用时显示，需要用户同意才能继续使用
class CrrAgreementDialog58 extends StatefulWidget {
  final VoidCallback onAccepted;
  final VoidCallback onRejected;

  const CrrAgreementDialog58({
    super.key,
    required this.onAccepted,
    required this.onRejected,
  });

  @override
  State<CrrAgreementDialog58> createState() => _CrrAgreementDialog58State();

  /// 检查用户是否已同意协议
  static Future<bool> hasUserAcceptedAgreement() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('user_agreement_accepted') ?? false;
  }

  /// 保存用户同意协议的状态
  static Future<void> saveAgreementAcceptance() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('user_agreement_accepted', true);
    await prefs.setString('agreement_accepted_date', DateTime.now().toIso8601String());
  }

  /// 清除协议同意状态（用于测试或重置）
  static Future<void> clearAgreementAcceptance() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('user_agreement_accepted');
    await prefs.remove('agreement_accepted_date');
  }

  /// 显示协议弹窗
  static Future<void> showAgreementDialog(
    BuildContext context, {
    required VoidCallback onAccepted,
    required VoidCallback onRejected,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false, // 用户必须做出选择
      builder: (BuildContext context) {
        return CrrAgreementDialog58(
          onAccepted: onAccepted,
          onRejected: onRejected,
        );
      },
    );
  }
}

class _CrrAgreementDialog58State extends State<CrrAgreementDialog58> {
  final ScrollController _scrollController = ScrollController();
  bool _isLoading = false;

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(16),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        decoration: BoxDecoration(
          color: CrrNeumorphic93.backgroundColor,
          borderRadius: BorderRadius.circular(20),
          boxShadow: CrrNeumorphic93.getElevatedShadow(),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            Expanded(child: _buildContent()),
            _buildButtons(),
          ],
        ),
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: CrrNeumorphic93.primaryColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.description,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              'Terms of Service & Privacy Policy',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 说明文字
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: CrrNeumorphic93.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              'Please read and accept our Terms of Service and Privacy Policy to continue using Crrie.',
              style: TextStyle(
                fontSize: 14,
                color: CrrNeumorphic93.textSecondary,
                height: 1.4,
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // 协议内容
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: CrrNeumorphic93.textSecondary.withOpacity(0.2),
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Scrollbar(
                controller: _scrollController,
                thumbVisibility: true,
                child: SingleChildScrollView(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    CrrAgreementTexts92.getFullAgreementText(),
                    style: const TextStyle(
                      fontSize: 12,
                      color: CrrNeumorphic93.textPrimary,
                      height: 1.5,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建按钮区域
  Widget _buildButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // 拒绝按钮
          Expanded(
            child: CrrNeumorphic93.container(
              height: 48,
              borderRadius: 24,
              backgroundColor: CrrNeumorphic93.cardColor,
              onTap: _isLoading ? null : _handleReject,
              child: const Center(
                child: Text(
                  'Decline',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: CrrNeumorphic93.textSecondary,
                  ),
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // 同意按钮
          Expanded(
            child: CrrNeumorphic93.container(
              height: 48,
              borderRadius: 24,
              backgroundColor: CrrNeumorphic93.primaryColor,
              onTap: _isLoading ? null : _handleAccept,
              child: Center(
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        'Accept',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 处理用户同意
  void _handleAccept() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await CrrAgreementDialog58.saveAgreementAcceptance();
      widget.onAccepted();
    } catch (e) {
      // 处理错误
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to save agreement. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 处理用户拒绝
  void _handleReject() {
    widget.onRejected();
  }
}
