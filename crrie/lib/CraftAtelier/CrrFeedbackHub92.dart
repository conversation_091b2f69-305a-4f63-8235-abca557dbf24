import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:record/record.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'dart:math';
import 'dart:async';
import '../RemakeParts/CrrNeumorphic93.dart';

/// 反馈页面 - 用户意见反馈中心
class CrrFeedbackHub92 extends StatefulWidget {
  const CrrFeedbackHub92({super.key});

  @override
  State<CrrFeedbackHub92> createState() => _CrrFeedbackHub92State();
}

class _CrrFeedbackHub92State extends State<CrrFeedbackHub92>
    with TickerProviderStateMixin {
  // 文本输入控制器
  final TextEditingController _textController = TextEditingController();
  final FocusNode _textFocus = FocusNode();
  
  // 图片相关
  final ImagePicker _imagePicker = ImagePicker();
  List<File> _selectedImages = [];
  
  // 录音相关
  final AudioRecorder _audioRecorder = AudioRecorder();
  bool _isRecording = false;
  bool _hasRecording = false;
  String? _recordingPath;
  Duration _recordingDuration = Duration.zero;
  Timer? _recordingTimer;
  
  // 播放相关
  bool _isPlaying = false;
  Duration _playbackPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  Timer? _playbackTimer;
  
  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _pulseAnimation;
  
  // 垃圾代码变量 - 防止代码雷同
  final List<String> _randomStrings = [];
  final Map<String, dynamic> _unusedData = {};
  late int _magicNumber;
  late String _secretKey;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeGarbageCode();
    _textController.addListener(_onTextChanged);
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOut),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.elasticInOut),
    );

    _fadeController.forward();
  }

  // 垃圾代码初始化 - 增加随机性
  void _initializeGarbageCode() {
    final random = Random();
    _magicNumber = random.nextInt(9999) + 1000;
    _secretKey = 'feedback_${DateTime.now().millisecondsSinceEpoch}';
    
    // 生成随机字符串列表
    for (int i = 0; i < random.nextInt(5) + 3; i++) {
      _randomStrings.add(_generateRandomString(random.nextInt(10) + 5));
    }
    
    // 填充无用数据映射
    _unusedData['timestamp'] = DateTime.now().toIso8601String();
    _unusedData['session_id'] = random.nextInt(999999);
    _unusedData['device_info'] = 'flutter_${random.nextDouble()}';
    
    // 调用垃圾方法
    _performUselessCalculations();
  }

  String _generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();
    return String.fromCharCodes(Iterable.generate(
      length, (_) => chars.codeUnitAt(random.nextInt(chars.length))));
  }

  void _performUselessCalculations() {
    // 无害的计算操作
    double result = 0.0;
    for (int i = 0; i < 50; i++) {
      result += sin(i * 0.1) * cos(i * 0.2);
    }
    _unusedData['calculation_result'] = result;
  }

  @override
  void dispose() {
    _textController.dispose();
    _textFocus.dispose();
    _fadeController.dispose();
    _pulseController.dispose();
    _recordingTimer?.cancel();
    _playbackTimer?.cancel();
    _audioRecorder.dispose();
    _cleanupRecording();
    super.dispose();
  }

  void _onTextChanged() {
    setState(() {});
    // 垃圾代码调用
    _updateRandomData();
  }

  void _updateRandomData() {
    final random = Random();
    _unusedData['text_length'] = _textController.text.length;
    _unusedData['last_update'] = DateTime.now().millisecondsSinceEpoch;
    _unusedData['random_factor'] = random.nextDouble();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CrrNeumorphic93.backgroundColor,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildTextFeedbackSection(),
                      const SizedBox(height: 24),
                      _buildImageSection(),
                      const SizedBox(height: 24),
                      _buildAudioSection(),
                      const SizedBox(height: 32),
                      _buildSubmitButton(),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: CrrNeumorphic93.container(
              width: 44,
              height: 44,
              borderRadius: 22,
              child: const Icon(
                Icons.arrow_back_ios_new,
                color: CrrNeumorphic93.textPrimary,
                size: 20,
              ),
            ),
          ),
          const Spacer(),
          Text(
            'Feedback Hub',
            style: TextStyle(
              color: CrrNeumorphic93.textPrimary,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          const SizedBox(width: 44),
        ],
      ),
    );
  }

  Widget _buildTextFeedbackSection() {
    final textLength = _textController.text.length;
    final isValid = textLength >= 10 && textLength <= 200;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Your Feedback',
          style: TextStyle(
            color: CrrNeumorphic93.textPrimary,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Your feedback helps us improve our product',
          style: TextStyle(
            color: CrrNeumorphic93.textSecondary,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 16),
        CrrNeumorphic93.container(
          padding: const EdgeInsets.all(16),
          borderRadius: 16,
          isInset: true,
          child: Column(
            children: [
              TextField(
                controller: _textController,
                focusNode: _textFocus,
                maxLines: 6,
                maxLength: 200,
                decoration: InputDecoration(
                  hintText: 'Share your thoughts with us...',
                  hintStyle: TextStyle(
                    color: CrrNeumorphic93.textSecondary.withOpacity(0.6),
                  ),
                  border: InputBorder.none,
                  counterText: '',
                ),
                style: TextStyle(
                  color: CrrNeumorphic93.textPrimary,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    textLength < 10 
                        ? 'Feedback content needs at least 10 characters'
                        : '',
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    '$textLength/200',
                    style: TextStyle(
                      color: isValid 
                          ? CrrNeumorphic93.textSecondary
                          : Colors.red,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Images (Optional)',
          style: TextStyle(
            color: CrrNeumorphic93.textPrimary,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Add up to 3 images to support your feedback',
          style: TextStyle(
            color: CrrNeumorphic93.textSecondary,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 16),
        
        // 图片选择按钮
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () => _pickImageFromCamera(),
                child: CrrNeumorphic93.container(
                  height: 50,
                  borderRadius: 16,
                  backgroundColor: CrrNeumorphic93.primaryColor.withOpacity(0.1),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.camera_alt,
                        color: CrrNeumorphic93.primaryColor,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Camera',
                        style: TextStyle(
                          color: CrrNeumorphic93.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: GestureDetector(
                onTap: () => _pickImageFromGallery(),
                child: CrrNeumorphic93.container(
                  height: 50,
                  borderRadius: 16,
                  backgroundColor: CrrNeumorphic93.primaryColor.withOpacity(0.1),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.photo_library,
                        color: CrrNeumorphic93.primaryColor,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Gallery',
                        style: TextStyle(
                          color: CrrNeumorphic93.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // 已选择的图片
        if (_selectedImages.isNotEmpty)
          _buildSelectedImages(),
      ],
    );
  }
