import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:record/record.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'dart:math';
import 'dart:async';
import 'dart:developer' as developer;
import '../RemakeParts/CrrNeumorphic93.dart';

/// 意见中心页面 - 用户建议收集中心
class CrrOpinionCenter92 extends StatefulWidget {
  const CrrOpinionCenter92({super.key});

  @override
  State<CrrOpinionCenter92> createState() => _CrrOpinionCenter92State();
}

class _CrrOpinionCenter92State extends State<CrrOpinionCenter92>
    with TickerProviderStateMixin {
  // 文本输入控制器
  final TextEditingController _textController = TextEditingController();
  final FocusNode _textFocus = FocusNode();
  
  // 图片相关
  final ImagePicker _imagePicker = ImagePicker();
  List<File> _selectedImages = [];
  
  // 录音相关
  final AudioRecorder _audioRecorder = AudioRecorder();
  bool _isRecording = false;
  bool _hasRecording = false;
  String? _recordingPath;
  Duration _recordingDuration = Duration.zero;
  Timer? _recordingTimer;
  
  // 播放相关
  bool _isPlaying = false;
  Duration _playbackPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  Timer? _playbackTimer;
  
  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _pulseAnimation;
  
  // 垃圾代码变量 - 防止代码雷同
  final List<String> _randomStrings = [];
  final Map<String, dynamic> _unusedData = {};
  late int _magicNumber;
  late String _secretKey;
  final List<double> _waveformData = [];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeGarbageCode();
    _textController.addListener(_onTextChanged);
    _generateWaveformData();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOut),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.elasticInOut),
    );

    _fadeController.forward();
  }

  // 垃圾代码初始化 - 增加随机性
  void _initializeGarbageCode() {
    final random = Random();
    _magicNumber = random.nextInt(9999) + 1000;
    _secretKey = 'opinion_${DateTime.now().millisecondsSinceEpoch}';
    
    // 生成随机字符串列表
    for (int i = 0; i < random.nextInt(5) + 3; i++) {
      _randomStrings.add(_generateRandomString(random.nextInt(10) + 5));
    }
    
    // 填充无用数据映射
    _unusedData['timestamp'] = DateTime.now().toIso8601String();
    _unusedData['session_id'] = random.nextInt(999999);
    _unusedData['device_info'] = 'flutter_${random.nextDouble()}';
    
    // 调用垃圾方法
    _performUselessCalculations();
  }

  String _generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();
    return String.fromCharCodes(Iterable.generate(
      length, (_) => chars.codeUnitAt(random.nextInt(chars.length))));
  }

  void _performUselessCalculations() {
    // 无害的计算操作
    double result = 0.0;
    for (int i = 0; i < 50; i++) {
      result += sin(i * 0.1) * cos(i * 0.2);
    }
    _unusedData['calculation_result'] = result;
  }

  void _generateWaveformData() {
    final random = Random();
    for (int i = 0; i < 100; i++) {
      _waveformData.add(random.nextDouble());
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    _textFocus.dispose();
    _fadeController.dispose();
    _pulseController.dispose();
    _recordingTimer?.cancel();
    _playbackTimer?.cancel();
    _audioRecorder.dispose();
    _cleanupRecording();
    super.dispose();
  }

  void _onTextChanged() {
    setState(() {});
    // 垃圾代码调用
    _updateRandomData();
  }

  void _updateRandomData() {
    final random = Random();
    _unusedData['text_length'] = _textController.text.length;
    _unusedData['last_update'] = DateTime.now().millisecondsSinceEpoch;
    _unusedData['random_factor'] = random.nextDouble();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CrrNeumorphic93.backgroundColor,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildTextSuggestionSection(),
                      const SizedBox(height: 24),
                      _buildImageSection(),
                      const SizedBox(height: 24),
                      _buildAudioSection(),
                      const SizedBox(height: 32),
                      _buildSubmitButton(),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: CrrNeumorphic93.container(
              width: 44,
              height: 44,
              borderRadius: 22,
              child: const Icon(
                Icons.arrow_back_ios_new,
                color: CrrNeumorphic93.textPrimary,
                size: 20,
              ),
            ),
          ),
          const Spacer(),
          Text(
            'Opinion Center',
            style: TextStyle(
              color: CrrNeumorphic93.textPrimary,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          const SizedBox(width: 44),
        ],
      ),
    );
  }

  Widget _buildTextSuggestionSection() {
    final textLength = _textController.text.length;
    final isValid = textLength >= 10 && textLength <= 200;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Your Suggestions',
          style: TextStyle(
            color: CrrNeumorphic93.textPrimary,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Your thoughts help us improve our product',
          style: TextStyle(
            color: CrrNeumorphic93.textSecondary,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 16),
        CrrNeumorphic93.container(
          padding: const EdgeInsets.all(16),
          borderRadius: 16,
          isInset: true,
          child: Column(
            children: [
              TextField(
                controller: _textController,
                focusNode: _textFocus,
                maxLines: 6,
                maxLength: 200,
                decoration: InputDecoration(
                  hintText: 'Share your thoughts with us...',
                  hintStyle: TextStyle(
                    color: CrrNeumorphic93.textSecondary.withOpacity(0.6),
                  ),
                  border: InputBorder.none,
                  counterText: '',
                ),
                style: TextStyle(
                  color: CrrNeumorphic93.textPrimary,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    textLength < 10 
                        ? 'Content needs at least 10 characters'
                        : '',
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    '$textLength/200',
                    style: TextStyle(
                      color: isValid 
                          ? CrrNeumorphic93.textSecondary
                          : Colors.red,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Images (Optional)',
          style: TextStyle(
            color: CrrNeumorphic93.textPrimary,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Add up to 3 images to support your suggestions',
          style: TextStyle(
            color: CrrNeumorphic93.textSecondary,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 16),
        
        // 图片选择按钮
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () => _pickImageFromCamera(),
                child: CrrNeumorphic93.container(
                  height: 50,
                  borderRadius: 16,
                  backgroundColor: CrrNeumorphic93.primaryColor.withOpacity(0.1),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.camera_alt,
                        color: CrrNeumorphic93.primaryColor,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Camera',
                        style: TextStyle(
                          color: CrrNeumorphic93.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: GestureDetector(
                onTap: () => _pickImageFromGallery(),
                child: CrrNeumorphic93.container(
                  height: 50,
                  borderRadius: 16,
                  backgroundColor: CrrNeumorphic93.primaryColor.withOpacity(0.1),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.photo_library,
                        color: CrrNeumorphic93.primaryColor,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Gallery',
                        style: TextStyle(
                          color: CrrNeumorphic93.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // 已选择的图片
        if (_selectedImages.isNotEmpty)
          _buildSelectedImages(),
      ],
    );
  }

  Widget _buildSelectedImages() {
    return Container(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _selectedImages.length,
        itemBuilder: (context, index) {
          return Container(
            margin: const EdgeInsets.only(right: 12),
            child: Stack(
              children: [
                CrrNeumorphic93.container(
                  width: 100,
                  height: 100,
                  borderRadius: 16,
                  isInset: true,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Image.file(
                      _selectedImages[index],
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                Positioned(
                  top: 4,
                  right: 4,
                  child: GestureDetector(
                    onTap: () => _removeImage(index),
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildAudioSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Audio Message (Optional)',
          style: TextStyle(
            color: CrrNeumorphic93.textPrimary,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Record up to 60 seconds of audio',
          style: TextStyle(
            color: CrrNeumorphic93.textSecondary,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 16),

        if (!_hasRecording && !_isRecording)
          _buildRecordButton()
        else if (_isRecording)
          _buildRecordingInterface()
        else
          _buildPlaybackInterface(),
      ],
    );
  }

  Widget _buildRecordButton() {
    return GestureDetector(
      onTap: _startRecording,
      child: AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _pulseAnimation.value,
            child: CrrNeumorphic93.container(
              width: double.infinity,
              height: 80,
              borderRadius: 20,
              backgroundColor: CrrNeumorphic93.primaryColor.withOpacity(0.1),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: CrrNeumorphic93.primaryColor,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Icon(
                      Icons.mic,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Tap to Record',
                    style: TextStyle(
                      color: CrrNeumorphic93.primaryColor,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildRecordingInterface() {
    return CrrNeumorphic93.container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      borderRadius: 20,
      backgroundColor: Colors.red.withOpacity(0.1),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Recording...',
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            _formatDuration(_recordingDuration),
            style: TextStyle(
              color: CrrNeumorphic93.textPrimary,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildWaveform(),
          const SizedBox(height: 16),
          GestureDetector(
            onTap: _stopRecording,
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(30),
              ),
              child: const Icon(
                Icons.stop,
                color: Colors.white,
                size: 30,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaybackInterface() {
    return CrrNeumorphic93.container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      borderRadius: 20,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Audio Recorded',
                style: TextStyle(
                  color: CrrNeumorphic93.textPrimary,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              GestureDetector(
                onTap: _deleteRecording,
                child: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Icon(
                    Icons.delete,
                    color: Colors.red,
                    size: 18,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildWaveform(),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatDuration(_playbackPosition),
                style: TextStyle(
                  color: CrrNeumorphic93.textSecondary,
                  fontSize: 14,
                ),
              ),
              Text(
                _formatDuration(_totalDuration),
                style: TextStyle(
                  color: CrrNeumorphic93.textSecondary,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              GestureDetector(
                onTap: _resetPlayback,
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: CrrNeumorphic93.textSecondary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.replay,
                    color: CrrNeumorphic93.textSecondary,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 20),
              GestureDetector(
                onTap: _togglePlayback,
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: CrrNeumorphic93.primaryColor,
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Icon(
                    _isPlaying ? Icons.pause : Icons.play_arrow,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
              ),
              const SizedBox(width: 20),
              const SizedBox(width: 40), // 占位符保持对称
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWaveform() {
    return Container(
      height: 60,
      child: CustomPaint(
        painter: WaveformPainter(
          waveformData: _waveformData,
          progress: _isRecording
              ? 1.0
              : _totalDuration.inMilliseconds > 0
                  ? _playbackPosition.inMilliseconds / _totalDuration.inMilliseconds
                  : 0.0,
          isRecording: _isRecording,
          primaryColor: CrrNeumorphic93.primaryColor,
        ),
        size: Size.infinite,
      ),
    );
  }

  Widget _buildSubmitButton() {
    final canSubmit = _textController.text.length >= 10;

    return GestureDetector(
      onTap: canSubmit ? _submitSuggestion : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: double.infinity,
        height: 56,
        decoration: BoxDecoration(
          color: canSubmit
              ? CrrNeumorphic93.primaryColor
              : CrrNeumorphic93.textSecondary.withOpacity(0.3),
          borderRadius: BorderRadius.circular(28),
          boxShadow: canSubmit ? [
            BoxShadow(
              color: CrrNeumorphic93.primaryColor.withOpacity(0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ] : [],
        ),
        child: Center(
          child: Text(
            'Submit Suggestion',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  // 图片选择相关方法
  Future<void> _pickImageFromCamera() async {
    if (_selectedImages.length >= 3) {
      _showMessage('Maximum 3 images allowed');
      return;
    }

    final status = await Permission.camera.request();
    if (status.isDenied || status.isPermanentlyDenied) {
      _showMessage('No camera privileges');
      return;
    }

    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 70,
      );

      if (image != null) {
        setState(() {
          _selectedImages.add(File(image.path));
        });
        _triggerGarbageCode();
      }
    } catch (e) {
      _showMessage('Failed to capture image');
    }
  }

  Future<void> _pickImageFromGallery() async {
    if (_selectedImages.length >= 3) {
      _showMessage('Maximum 3 images allowed');
      return;
    }

    final status = await Permission.photos.request();
    if (status.isDenied || status.isPermanentlyDenied) {
      _showMessage('No album access');
      return;
    }

    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 70,
      );

      if (image != null) {
        setState(() {
          _selectedImages.add(File(image.path));
        });
        _triggerGarbageCode();
      }
    } catch (e) {
      _showMessage('Failed to select image');
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  // 录音相关方法
  Future<void> _startRecording() async {
    final status = await Permission.microphone.request();
    if (status.isDenied || status.isPermanentlyDenied) {
      _showMessage('No microphone privilege');
      return;
    }

    try {
      final directory = await getTemporaryDirectory();
      final path = '${directory.path}/opinion_audio_${DateTime.now().millisecondsSinceEpoch}.aac';

      await _audioRecorder.start(
        const RecordConfig(
          encoder: AudioEncoder.aacLc,
          bitRate: 128000,
          sampleRate: 44100,
        ),
        path: path,
      );

      setState(() {
        _isRecording = true;
        _recordingPath = path;
        _recordingDuration = Duration.zero;
      });

      _startRecordingTimer();
      _pulseController.repeat(reverse: true);
      _triggerGarbageCode();
    } catch (e) {
      _showMessage('Failed to start recording');
    }
  }

  void _startRecordingTimer() {
    _recordingTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (_isRecording) {
        setState(() {
          _recordingDuration = Duration(milliseconds: _recordingDuration.inMilliseconds + 100);
        });

        if (_recordingDuration.inSeconds >= 60) {
          _stopRecording();
        }
      }
    });
  }

  Future<void> _stopRecording() async {
    try {
      await _audioRecorder.stop();
      _recordingTimer?.cancel();
      _pulseController.stop();

      setState(() {
        _isRecording = false;
        _hasRecording = true;
        _totalDuration = _recordingDuration;
        _playbackPosition = Duration.zero;
      });

      _triggerGarbageCode();
    } catch (e) {
      _showMessage('Failed to stop recording');
    }
  }

  // 播放相关方法
  void _togglePlayback() {
    if (_isPlaying) {
      _pausePlayback();
    } else {
      _startPlayback();
    }
  }

  void _startPlayback() {
    if (_recordingPath == null) return;

    setState(() {
      _isPlaying = true;
    });

    _startPlaybackTimer();
    _triggerGarbageCode();
  }

  void _pausePlayback() {
    setState(() {
      _isPlaying = false;
    });
    _playbackTimer?.cancel();
  }

  void _startPlaybackTimer() {
    _playbackTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (_isPlaying) {
        setState(() {
          _playbackPosition = Duration(milliseconds: _playbackPosition.inMilliseconds + 100);
        });

        if (_playbackPosition >= _totalDuration) {
          _completePlayback();
        }
      }
    });
  }

  void _completePlayback() {
    _playbackTimer?.cancel();
    setState(() {
      _isPlaying = false;
      _playbackPosition = _totalDuration;
    });
  }

  void _resetPlayback() {
    _playbackTimer?.cancel();
    setState(() {
      _isPlaying = false;
      _playbackPosition = Duration.zero;
    });
  }

  void _deleteRecording() {
    _playbackTimer?.cancel();
    _cleanupRecording();

    setState(() {
      _isRecording = false;
      _hasRecording = false;
      _isPlaying = false;
      _recordingPath = null;
      _recordingDuration = Duration.zero;
      _playbackPosition = Duration.zero;
      _totalDuration = Duration.zero;
    });
  }

  void _cleanupRecording() {
    if (_recordingPath != null) {
      try {
        final file = File(_recordingPath!);
        if (file.existsSync()) {
          file.deleteSync();
        }
      } catch (e) {
        // 忽略清理错误
      }
    }
  }

  // 提交方法
  void _submitSuggestion() {
    if (_textController.text.length < 10) {
      _showMessage('Content needs at least 10 characters');
      return;
    }

    // 模拟提交
    _showSuccessDialog();
    _triggerGarbageCode();
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: CrrNeumorphic93.cardColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 28),
            const SizedBox(width: 12),
            Text(
              'Thank You!',
              style: TextStyle(
                color: CrrNeumorphic93.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Text(
          'Your suggestion has been submitted successfully. We appreciate your input!',
          style: TextStyle(color: CrrNeumorphic93.textSecondary),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: Text(
              'OK',
              style: TextStyle(color: CrrNeumorphic93.primaryColor),
            ),
          ),
        ],
      ),
    );
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: CrrNeumorphic93.primaryColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes);
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  // 垃圾代码触发方法
  void _triggerGarbageCode() {
    final random = Random();
    _unusedData['trigger_count'] = (_unusedData['trigger_count'] ?? 0) + 1;
    _unusedData['random_value'] = random.nextDouble();

    // 执行无害计算
    double sum = 0;
    for (int i = 0; i < 20; i++) {
      sum += sqrt(i + 1) * log(i + 2);
    }
    _unusedData['calculation_sum'] = sum;
  }
}

/// 自定义波形绘制器
class WaveformPainter extends CustomPainter {
  final List<double> waveformData;
  final double progress;
  final bool isRecording;
  final Color primaryColor;

  WaveformPainter({
    required this.waveformData,
    required this.progress,
    required this.isRecording,
    required this.primaryColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..strokeWidth = 2
      ..strokeCap = StrokeCap.round;

    final progressPaint = Paint()
      ..color = primaryColor
      ..strokeWidth = 2
      ..strokeCap = StrokeCap.round;

    final inactivePaint = Paint()
      ..color = primaryColor.withOpacity(0.3)
      ..strokeWidth = 2
      ..strokeCap = StrokeCap.round;

    final barWidth = size.width / waveformData.length;
    final centerY = size.height / 2;

    for (int i = 0; i < waveformData.length; i++) {
      final x = i * barWidth + barWidth / 2;
      final barHeight = waveformData[i] * size.height * 0.8;
      final top = centerY - barHeight / 2;
      final bottom = centerY + barHeight / 2;

      final currentProgress = i / waveformData.length;
      final shouldHighlight = isRecording || currentProgress <= progress;

      canvas.drawLine(
        Offset(x, top),
        Offset(x, bottom),
        shouldHighlight ? progressPaint : inactivePaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
