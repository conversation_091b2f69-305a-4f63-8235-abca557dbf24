import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../ReclaimFlow/CrrUserProvider52.dart';
import '../ScrapVault/CrrSoulQuest72.dart';
import '../RemakeParts/CrrNeumorphic93.dart';

/// 每日启发页面
/// 展示每日创意启发问题和历史回答
class CrrSoulQuestPage45 extends StatefulWidget {
  const CrrSoulQuestPage45({super.key});

  @override
  State<CrrSoulQuestPage45> createState() => _CrrSoulQuestPage45State();
}

class _CrrSoulQuestPage45State extends State<CrrSoulQuestPage45>
    with TickerProviderStateMixin {
  late AnimationController _animController;
  final TextEditingController _answerController = TextEditingController();
  bool _isSubmitting = false;
  List<CrrSoulAnswer84> _answerHistory = [];

  @override
  void initState() {
    super.initState();
    _animController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _animController.forward();
    _loadAnswerHistory();
  }

  /// 加载回答历史
  void _loadAnswerHistory() async {
    final userProvider = Provider.of<CrrUserProvider52>(context, listen: false);
    final history = await userProvider.getSoulAnswers();
    if (mounted) {
      setState(() {
        _answerHistory = history;
      });
    }
  }

  @override
  void dispose() {
    _animController.dispose();
    _answerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CrrNeumorphic93.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: 24),
              _buildTodayQuestion(),
              const SizedBox(height: 24),
              // _buildStatsCard(),
              // const SizedBox(height: 24),
              _buildRecentAnswers(),
              const SizedBox(height: 100), // 底部导航栏空间
            ],
          ),
        ),
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            // Container(
            //   width: 50,
            //   height: 50,
            //   decoration: BoxDecoration(
            //     shape: BoxShape.circle,
            //     color: Colors.purple,
            //     boxShadow: CrrNeumorphic93.getElevatedShadow(),
            //   ),
            //   child: const Icon(
            //     Icons.psychology_rounded,
            //     color: Colors.white,
            //     size: 28,
            //   ),
            // ).animate(controller: _animController)
            //   .scale(duration: 600.ms, curve: Curves.elasticOut),
            //
            // const SizedBox(width: 16),
            
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    child: Stack(
                      children: [
                        // 底层阴影 - 深色
                        Positioned(
                          left: 2,
                          top: 2,
                          child: Text(
                            'Inspire',
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: Colors.black.withOpacity(0.3),
                            ),
                          ),
                        ),
                        // 中层阴影 - 中等深度
                        Positioned(
                          left: 1,
                          top: 1,
                          child: Text(
                            'Inspire',
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: Colors.black.withOpacity(0.2),
                            ),
                          ),
                        ),
                        // 顶层文字 - 渐变效果
                        ShaderMask(
                          shaderCallback: (bounds) => LinearGradient(
                            colors: [
                              CrrNeumorphic93.primaryColor,
                              CrrNeumorphic93.primaryColor.withOpacity(0.8),
                              CrrNeumorphic93.accentColor,
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ).createShader(bounds),
                          child: Text(
                            'Inspire',
                            style: const TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              shadows: [
                                Shadow(
                                  color: Colors.white,
                                  offset: Offset(-1, -1),
                                  blurRadius: 2,
                                ),
                                Shadow(
                                  color: Colors.black26,
                                  offset: Offset(1, 1),
                                  blurRadius: 3,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ).animate(controller: _animController)
                    .fadeIn(duration: 600.ms)
                    .slideX(begin: -0.3, end: 0),

                  // Text(
                  //   'Creative thinking challenges',
                  //   style: TextStyle(
                  //     fontSize: 14,
                  //     color: CrrNeumorphic93.textSecondary,
                  //   ),
                  // ).animate(controller: _animController)
                  //   .fadeIn(duration: 600.ms, delay: 200.ms)
                  //   .slideX(begin: -0.3, end: 0),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建今日问题
  Widget _buildTodayQuestion() {
    return Consumer<CrrUserProvider52>(
      builder: (context, userProvider, child) {
        final user = userProvider.currentUser;
        final todayQuestion = CrrSoulQuestRepo95.getTodayQuestion();
        final canAnswer = user?.canAnswerSoulQuestionToday ?? false;

        return CrrNeumorphic93.card(
          padding: const EdgeInsets.all(24),
          borderRadius: 24,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 头部
              Row(
                children: [
                  Text(
                    'Today\'s Challenge',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: CrrNeumorphic93.textPrimary,
                    ),
                  ),
                  const Spacer(),
                  if (canAnswer)
                    CrrNeumorphic93.container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      borderRadius: 12,
                      backgroundColor: CrrNeumorphic93.accentColor,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.diamond,
                            color: Colors.white,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '+${todayQuestion.rewardCoins} ',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // 问题内容
              CrrNeumorphic93.container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                borderRadius: 16,
                isInset: true,
                child: Text(
                  todayQuestion.questionText,
                  style: TextStyle(
                    color: CrrNeumorphic93.textPrimary,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    height: 1.5,
                  ),
                ),
              ),
              
              if (canAnswer) ...[
                const SizedBox(height: 20),
                
                // 回答输入框
                CrrNeumorphic93.textField(
                  controller: _answerController,
                  hintText: 'Share your thoughts and reflections...',
                  maxLines: 4,
                  borderRadius: 16,
                  textInputAction: TextInputAction.done,
                  onSubmitted: (_) => _submitAnswer(),
                ),
                
                const SizedBox(height: 16),
                
                // 提交按钮
                CrrNeumorphic93.button(
                  onPressed: _isSubmitting ? () {} : _submitAnswer,
                  height: 50,
                  borderRadius: 16,
                  backgroundColor: Colors.purple,
                  isLoading: _isSubmitting,
                  child: const Text(
                    'Submit Answer',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                ),
              ] else ...[
                const SizedBox(height: 16),
                CrrNeumorphic93.container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  borderRadius: 16,
                  backgroundColor: CrrNeumorphic93.textSecondary.withOpacity(0.1),
                  child: Text(
                    'You\'ve already answered today\'s question. Come back tomorrow for a new one!',
                    style: TextStyle(
                      color: CrrNeumorphic93.textSecondary,
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ],
          ),
        ).animate(controller: _animController)
          .fadeIn(duration: 600.ms, delay: 400.ms)
          .slideY(begin: 0.3, end: 0);
      },
    );
  }

  /// 构建统计卡片
  Widget _buildStatsCard() {
    return Consumer<CrrUserProvider52>(
      builder: (context, userProvider, child) {
        final user = userProvider.currentUser;
        
        return Row(
          children: [
            Expanded(
              child: CrrNeumorphic93.card(
                padding: const EdgeInsets.all(20),
                borderRadius: 20,
                child: Column(
                  children: [
                    Text(
                      '${user?.answeredSoulQuestions ?? 0}',
                      style: TextStyle(
                        color: Colors.purple,
                        fontWeight: FontWeight.bold,
                        fontSize: 24,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Completed',
                      style: TextStyle(
                        color: CrrNeumorphic93.textSecondary,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: CrrNeumorphic93.card(
                padding: const EdgeInsets.all(20),
                borderRadius: 20,
                child: Column(
                  children: [
                    Text(
                      '${(user?.answeredSoulQuestions ?? 0) * 10}',
                      style: TextStyle(
                        color: CrrNeumorphic93.accentColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 24,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Earned',
                      style: TextStyle(
                        color: CrrNeumorphic93.textSecondary,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ).animate(controller: _animController)
          .fadeIn(duration: 600.ms, delay: 600.ms)
          .slideY(begin: 0.3, end: 0);
      },
    );
  }

  /// 构建最近回答
  Widget _buildRecentAnswers() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [

        // 历史回答
        _buildAnswerHistory(),
      ],
    );
  }

  /// 提交回答
  void _submitAnswer() async {
    if (_answerController.text.trim().isEmpty) {
      _showMessage('Please write your answer first.');
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    final userProvider = context.read<CrrUserProvider52>();
    final todayQuestion = CrrSoulQuestRepo95.getTodayQuestion();
    
    final success = await userProvider.answerSoulQuestion(
      todayQuestion,
      _answerController.text.trim(),
    );

    setState(() {
      _isSubmitting = false;
    });

    if (success) {
      final todayQuestion = CrrSoulQuestRepo95.getTodayQuestion();
      _showMessage('Great! You earned ${todayQuestion.rewardCoins} gems! 🎉');
      _answerController.clear();
      _loadAnswerHistory(); // 重新加载历史
    } else {
      _showMessage('Something went wrong. Please try again.');
    }
  }

  /// 构建回答历史
  Widget _buildAnswerHistory() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Record',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: CrrNeumorphic93.textPrimary,
          ),
        ).animate(controller: _animController)
          .fadeIn(duration: 600.ms, delay: 800.ms)
          .slideX(begin: -0.3, end: 0),

        const SizedBox(height: 16),

        if (_answerHistory.isEmpty)
          CrrNeumorphic93.card(
            padding: const EdgeInsets.all(20),
            borderRadius: 20,
            child: Center(
              child: Column(
                children: [
                  Icon(
                    Icons.history,
                    size: 48,
                    color: CrrNeumorphic93.textSecondary,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Your challenge history will appear here',
                    style: TextStyle(
                      color: CrrNeumorphic93.textSecondary,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ).animate(controller: _animController)
            .fadeIn(duration: 600.ms, delay: 1000.ms)
            .slideY(begin: 0.3, end: 0)
        else
          ...(_answerHistory.take(3).map((answer) => Container(
            margin: const EdgeInsets.only(bottom: 12),
            child: CrrNeumorphic93.card(
              padding: const EdgeInsets.all(16),
              borderRadius: 16,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          _formatDate(answer.answeredAt),
                          style: TextStyle(
                            fontSize: 12,
                            color: CrrNeumorphic93.textSecondary,
                          ),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: CrrNeumorphic93.accentColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.diamond,
                              size: 12,
                              color: CrrNeumorphic93.accentColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '+${answer.coinsEarned}',
                              style: TextStyle(
                                fontSize: 10,
                                color: CrrNeumorphic93.accentColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    answer.answerText,
                    style: TextStyle(
                      fontSize: 14,
                      color: CrrNeumorphic93.textPrimary,
                      height: 1.4,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          )).toList()),
      ],
    );
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday';
    } else {
      return '${difference} days ago';
    }
  }

  /// 显示消息
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.purple,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
