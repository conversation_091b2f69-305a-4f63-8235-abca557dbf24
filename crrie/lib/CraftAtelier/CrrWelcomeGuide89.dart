import 'package:flutter/material.dart';
import '../RemakeParts/CrrNeumorphic93.dart';
import '../RemakeParts/CrrFloatingNav42.dart';

/// 应用启动引导页面
/// 展示应用主题、功能介绍和启动按钮
class CrrWelcomeGuide89 extends StatefulWidget {
  const CrrWelcomeGuide89({super.key});

  @override
  State<CrrWelcomeGuide89> createState() => _CrrWelcomeGuide89State();
}

class _CrrWelcomeGuide89State extends State<CrrWelcomeGuide89>
    with SingleTickerProviderStateMixin {
  late AnimationController _animController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _animController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animController,
      curve: Curves.easeInOut,
    ));

    _startAnimation();
  }

  @override
  void dispose() {
    _animController.dispose();
    super.dispose();
  }

  /// 启动动画
  void _startAnimation() async {
    await Future.delayed(const Duration(milliseconds: 500));
    _animController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CrrNeumorphic93.backgroundColor,
      body: SafeArea(
        child: Stack(
          children: [
            // 背景装饰
            _buildBackgroundDecoration(),
            
            // 主要内容
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  const Spacer(flex: 2),
                  
                  // Logo和标题区域
                  _buildLogoSection(),
                  
                  const Spacer(flex: 1),
                  
                  // 功能介绍区域
                  _buildFeatureSection(),
                  
                  const Spacer(flex: 2),
                  
                  // 启动按钮
                  _buildStartButton(),
                  
                  const SizedBox(height: 40),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建背景装饰
  Widget _buildBackgroundDecoration() {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Stack(
            children: [
              // 浮动圆圈装饰
              Positioned(
                top: 100,
                right: -50,
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: CrrNeumorphic93.primaryColor.withOpacity(0.1),
                  ),
                ),
              ),

              Positioned(
                bottom: 200,
                left: -80,
                child: Container(
                  width: 160,
                  height: 160,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: CrrNeumorphic93.accentColor.withOpacity(0.1),
                  ),
                ),
              ),

              Positioned(
                top: 300,
                left: 50,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.green.withOpacity(0.1),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建Logo和标题区域
  Widget _buildLogoSection() {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Transform.scale(
            scale: 0.8 + (_fadeAnimation.value * 0.2),
            child: Column(
              children: [
                // App Logo
                CrrNeumorphic93.container(
                  width: 120,
                  height: 120,
                  borderRadius: 60,
                  backgroundColor: CrrNeumorphic93.primaryColor,
                  child: const Icon(
                    Icons.recycling_rounded,
                    size: 60,
                    color: Colors.white,
                  ),
                ),

                const SizedBox(height: 24),

                // App名称
                const Text(
                  'Crrie',
                  style: TextStyle(
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                    color: CrrNeumorphic93.textPrimary,
                  ),
                ),

                const SizedBox(height: 8),

                // 副标题
                const Text(
                  'AI Household Waste Upcycling Guide',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    color: CrrNeumorphic93.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建功能介绍区域
  Widget _buildFeatureSection() {
    final features = [
      {
        'icon': Icons.chat_bubble_rounded,
        'title': 'AI Expert Chat',
        'description': 'Get creative upcycling ideas from AI specialists',
        'color': CrrNeumorphic93.primaryColor,
      },
      {
        'icon': Icons.favorite_rounded,
        'title': 'Save Favorites',
        'description': 'Bookmark your favorite experts and projects',
        'color': CrrNeumorphic93.accentColor,
      },
      {
        'icon': Icons.auto_awesome_rounded,
        'title': 'Daily Inspiration',
        'description': 'Answer soul questions and earn rewards',
        'color': Colors.purple,
      },
    ];

    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Column(
            children: features.asMap().entries.map((entry) {
              final index = entry.key;
              final feature = entry.value;

              return Container(
                margin: const EdgeInsets.only(bottom: 20),
                child: _buildFeatureCard(
                  feature['icon'] as IconData,
                  feature['title'] as String,
                  feature['description'] as String,
                  feature['color'] as Color,
                  index,
                ),
              );
            }).toList(),
          ),
        );
      },
    );
  }

  /// 构建功能卡片
  Widget _buildFeatureCard(IconData icon, String title, String description, Color color, int index) {
    return CrrNeumorphic93.card(
      padding: const EdgeInsets.all(20),
      borderRadius: 20,
      child: Row(
        children: [
          // 图标
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(25),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          
          const SizedBox(width: 16),
          
          // 文本内容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: CrrNeumorphic93.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: CrrNeumorphic93.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建启动按钮
  Widget _buildStartButton() {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: GestureDetector(
            onTap: _navigateToMainApp,
            child: Container(
        width: double.infinity,
        height: 56,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(28),
          gradient: LinearGradient(
            colors: [
              CrrNeumorphic93.primaryColor,
              CrrNeumorphic93.primaryColor.withOpacity(0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            // 外阴影 - 深色
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              offset: const Offset(6, 6),
              blurRadius: 12,
              spreadRadius: 0,
            ),
            // 外阴影 - 浅色
            BoxShadow(
              color: Colors.white.withOpacity(0.7),
              offset: const Offset(-6, -6),
              blurRadius: 12,
              spreadRadius: 0,
            ),
            // 内阴影效果
            BoxShadow(
              color: CrrNeumorphic93.primaryColor.withOpacity(0.5),
              offset: const Offset(2, 2),
              blurRadius: 4,
              spreadRadius: -2,
            ),
          ],
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(28),
            gradient: LinearGradient(
              colors: [
                Colors.white.withOpacity(0.1),
                Colors.transparent,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Get Started',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  shadows: [
                    Shadow(
                      color: Colors.black26,
                      offset: Offset(1, 1),
                      blurRadius: 2,
                    ),
                  ],
                ),
              ),
              SizedBox(width: 8),
              Icon(
                Icons.arrow_forward_rounded,
                color: Colors.white,
                size: 24,
                shadows: [
                  Shadow(
                    color: Colors.black26,
                    offset: Offset(1, 1),
                    blurRadius: 2,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    ));
      },
    );
  }

  /// 导航到主应用
  void _navigateToMainApp() {
    Navigator.pushReplacement(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const CrrMainContainer(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 500),
      ),
    );
  }
}
