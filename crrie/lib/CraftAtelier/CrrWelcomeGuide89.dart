import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../RemakeParts/CrrNeumorphic93.dart';
import '../RemakeParts/CrrFloatingNav42.dart';

/// 应用启动引导页面
/// 展示应用主题、功能介绍和启动按钮
class CrrWelcomeGuide89 extends StatefulWidget {
  const CrrWelcomeGuide89({super.key});

  @override
  State<CrrWelcomeGuide89> createState() => _CrrWelcomeGuide89State();
}

class _CrrWelcomeGuide89State extends State<CrrWelcomeGuide89>
    with TickerProviderStateMixin {
  late AnimationController _logoAnimController;
  late AnimationController _contentAnimController;
  late AnimationController _buttonAnimController;
  late AnimationController _backgroundAnimController;

  @override
  void initState() {
    super.initState();
    
    _logoAnimController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _contentAnimController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _buttonAnimController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _backgroundAnimController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _startAnimations();
  }

  @override
  void dispose() {
    _logoAnimController.dispose();
    _contentAnimController.dispose();
    _buttonAnimController.dispose();
    _backgroundAnimController.dispose();
    super.dispose();
  }

  /// 启动动画序列
  void _startAnimations() async {
    await Future.delayed(const Duration(milliseconds: 300));
    _backgroundAnimController.forward();
    
    await Future.delayed(const Duration(milliseconds: 500));
    _logoAnimController.forward();
    
    await Future.delayed(const Duration(milliseconds: 800));
    _contentAnimController.forward();
    
    await Future.delayed(const Duration(milliseconds: 1200));
    _buttonAnimController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CrrNeumorphic93.backgroundColor,
      body: SafeArea(
        child: Stack(
          children: [
            // 背景装饰
            _buildBackgroundDecoration(),
            
            // 主要内容
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  const Spacer(flex: 2),
                  
                  // Logo和标题区域
                  _buildLogoSection(),
                  
                  const Spacer(flex: 1),
                  
                  // 功能介绍区域
                  _buildFeatureSection(),
                  
                  const Spacer(flex: 2),
                  
                  // 启动按钮
                  _buildStartButton(),
                  
                  const SizedBox(height: 40),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建背景装饰
  Widget _buildBackgroundDecoration() {
    return Stack(
      children: [
        // 浮动圆圈装饰
        Positioned(
          top: 100,
          right: -50,
          child: Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: CrrNeumorphic93.primaryColor.withOpacity(0.1),
            ),
          ).animate(controller: _backgroundAnimController)
            .scale(begin: const Offset(0, 0), end: const Offset(1, 1))
            .fadeIn(),
        ),
        
        Positioned(
          bottom: 200,
          left: -80,
          child: Container(
            width: 160,
            height: 160,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: CrrNeumorphic93.accentColor.withOpacity(0.1),
            ),
          ).animate(controller: _backgroundAnimController)
            .scale(begin: const Offset(0, 0), end: const Offset(1, 1), delay: const Duration(milliseconds: 300))
            .fadeIn(delay: const Duration(milliseconds: 300)),
        ),
        
        Positioned(
          top: 300,
          left: 50,
          child: Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.green.withOpacity(0.1),
            ),
          ).animate(controller: _backgroundAnimController)
            .scale(begin: const Offset(0, 0), end: const Offset(1, 1), delay: const Duration(milliseconds: 600))
            .fadeIn(delay: const Duration(milliseconds: 600)),
        ),
      ],
    );
  }

  /// 构建Logo和标题区域
  Widget _buildLogoSection() {
    return Column(
      children: [
        // App Logo
        CrrNeumorphic93.container(
          width: 120,
          height: 120,
          borderRadius: 60,
          backgroundColor: CrrNeumorphic93.primaryColor,
          child: const Icon(
            Icons.recycling_rounded,
            size: 60,
            color: Colors.white,
          ),
        ).animate(controller: _logoAnimController)
          .scale(begin: const Offset(0.5, 0.5), end: const Offset(1, 1))
          .fadeIn()
          .then()
          .shimmer(duration: const Duration(milliseconds: 1000), color: Colors.white.withOpacity(0.3)),
        
        const SizedBox(height: 24),
        
        // App名称
        Text(
          'Crrie',
          style: const TextStyle(
            fontSize: 36,
            fontWeight: FontWeight.bold,
            color: CrrNeumorphic93.textPrimary,
          ),
        ).animate(controller: _logoAnimController)
          .slideY(begin: 0.5, end: 0, delay: const Duration(milliseconds: 300))
          .fadeIn(delay: const Duration(milliseconds: 300)),
        
        const SizedBox(height: 8),
        
        // 副标题
        Text(
          'AI Household Waste Upcycling Guide',
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontSize: 16,
            color: CrrNeumorphic93.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ).animate(controller: _logoAnimController)
          .slideY(begin: 0.5, end: 0, delay: const Duration(milliseconds: 600))
          .fadeIn(delay: const Duration(milliseconds: 600)),
      ],
    );
  }

  /// 构建功能介绍区域
  Widget _buildFeatureSection() {
    final features = [
      {
        'icon': Icons.chat_bubble_rounded,
        'title': 'AI Expert Chat',
        'description': 'Get creative upcycling ideas from AI specialists',
        'color': CrrNeumorphic93.primaryColor,
      },
      {
        'icon': Icons.favorite_rounded,
        'title': 'Save Favorites',
        'description': 'Bookmark your favorite experts and projects',
        'color': CrrNeumorphic93.accentColor,
      },
      {
        'icon': Icons.auto_awesome_rounded,
        'title': 'Daily Inspiration',
        'description': 'Answer soul questions and earn rewards',
        'color': Colors.purple,
      },
    ];

    return Column(
      children: features.asMap().entries.map((entry) {
        final index = entry.key;
        final feature = entry.value;

        return Container(
          margin: const EdgeInsets.only(bottom: 20),
          child: _buildFeatureCard(
            feature['icon'] as IconData,
            feature['title'] as String,
            feature['description'] as String,
            feature['color'] as Color,
            index,
          ),
        );
      }).toList(),
    );
  }

  /// 构建功能卡片
  Widget _buildFeatureCard(IconData icon, String title, String description, Color color, int index) {
    return CrrNeumorphic93.card(
      padding: const EdgeInsets.all(20),
      borderRadius: 20,
      child: Row(
        children: [
          // 图标
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(25),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          
          const SizedBox(width: 16),
          
          // 文本内容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: CrrNeumorphic93.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: CrrNeumorphic93.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ).animate(controller: _contentAnimController)
      .slideX(begin: 1, end: 0, delay: Duration(milliseconds: index * 200))
      .fadeIn(delay: Duration(milliseconds: index * 200));
  }

  /// 构建启动按钮
  Widget _buildStartButton() {
    return CrrNeumorphic93.container(
      width: double.infinity,
      height: 56,
      borderRadius: 28,
      backgroundColor: CrrNeumorphic93.primaryColor,
      onTap: _navigateToMainApp,
      child: const Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Get Started',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          SizedBox(width: 8),
          Icon(
            Icons.arrow_forward_rounded,
            color: Colors.white,
            size: 24,
          ),
        ],
      ),
    ).animate(controller: _buttonAnimController)
      .scale(begin: const Offset(0.8, 0.8), end: const Offset(1, 1))
      .fadeIn()
      .then()
      .shimmer(duration: const Duration(milliseconds: 2000), color: Colors.white.withOpacity(0.3));
  }

  /// 导航到主应用
  void _navigateToMainApp() {
    Navigator.pushReplacement(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const CrrMainContainer(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 500),
      ),
    );
  }
}
