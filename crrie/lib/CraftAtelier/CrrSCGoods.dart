/// 商品数据模型
class CrrSCGoods {
  final String code;
  final String exchangeCoin;
  final String price;
  final String tags;

  const CrrSCGoods({
    required this.code,
    required this.exchangeCoin,
    required this.price,
    required this.tags,
  });

  /// 获取金币数量（整数）
  int get coinAmount => int.tryParse(exchangeCoin) ?? 0;

  /// 获取价格（浮点数）
  double get priceAmount => double.tryParse(price) ?? 0.0;

  /// 是否有标签
  bool get hasTag => tags.isNotEmpty;

  /// 获取格式化的价格显示
  String get formattedPrice => '\$${price}';

  /// 获取格式化的金币显示
  String get formattedCoin => '${exchangeCoin} Coins';

  @override
  String toString() {
    return 'CrrSCGoods(code: $code, exchangeCoin: $exchangeCoin, price: $price, tags: $tags)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CrrSCGoods && other.code == code;
  }

  @override
  int get hashCode => code.hashCode;
}

/// 商品数据源管理
class CrrSCGoodsRepository {
  /// 预设商品列表
  static const List<CrrSCGoods> _presetGoods = [
    CrrSCGoods(
      code: "311400",
      exchangeCoin: "100",
      price: "0.99",
      tags: "",
    ),
    CrrSCGoods(
      code: "311401",
      exchangeCoin: "500",
      price: "4.99",
      tags: "Big Deal",
    ),
    CrrSCGoods(
      code: "311402",
      exchangeCoin: "1000",
      price: "9.99",
      tags: "Popular",
    ),
    CrrSCGoods(
      code: "311403",
      exchangeCoin: "2500",
      price: "19.99",
      tags: "Best Value",
    ),
    CrrSCGoods(
      code: "311404",
      exchangeCoin: "5000",
      price: "39.99",
      tags: "Premium",
    ),
    CrrSCGoods(
      code: "311405",
      exchangeCoin: "10000",
      price: "79.99",
      tags: "Ultimate",
    ),
  ];

  /// 商品码映射表
  static final Map<String, CrrSCGoods> _goodsMap = {
    for (var goods in _presetGoods) goods.code: goods
  };

  /// 获取所有商品
  static List<CrrSCGoods> getAllGoods() => List.unmodifiable(_presetGoods);

  /// 根据商品码获取商品
  static CrrSCGoods? getGoodsByCode(String code) => _goodsMap[code];

  /// 检查商品是否存在
  static bool isGoodsExist(String code) => _goodsMap.containsKey(code);

  /// 获取所有商品码
  static List<String> getAllProductIds() => _goodsMap.keys.toList();
}
