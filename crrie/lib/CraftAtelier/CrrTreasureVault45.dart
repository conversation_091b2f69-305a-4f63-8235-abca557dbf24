import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../RemakeParts/CrrNeumorphic93.dart';
import 'CrrSCGoods.dart';
import 'CrrCoinManager78.dart';
import 'CrrPurchaseManager91.dart';

/// 金币商城页面 - 宝藏金库
class CrrTreasureVault45 extends StatefulWidget {
  const CrrTreasureVault45({super.key});

  @override
  State<CrrTreasureVault45> createState() => _CrrTreasureVault45State();
}

class _CrrTreasureVault45State extends State<CrrTreasureVault45>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  bool _isLoading = false;
  final List<CrrSCGoods> _goodsList = CrrSCGoodsRepository.getAllGoods();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializePurchaseManager();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOut),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );

    _fadeController.forward();
    _scaleController.forward();
  }

  void _initializePurchaseManager() {
    final purchaseManager = CrrPurchaseManager91.instance;
    
    // 设置回调
    purchaseManager.onLoadingChanged = (isLoading) {
      if (mounted) {
        setState(() {
          _isLoading = isLoading;
        });
      }
    };

    purchaseManager.onSuccess = (message) {
      if (mounted) {
        _showSuccessDialog(message);
      }
    };

    purchaseManager.onError = (message) {
      if (mounted) {
        _showErrorDialog(message);
      }
    };

    purchaseManager.onCancelled = (message) {
      if (mounted) {
        _showCancelDialog(message);
      }
    };

    // 初始化
    purchaseManager.initialize();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CrrNeumorphic93.backgroundColor,
      body: Stack(
        children: [
          _buildMainContent(),
          if (_isLoading) _buildLoadingOverlay(),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    return SafeArea(
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: _buildGoodsGrid(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // 顶部导航栏
          Row(
            children: [
              GestureDetector(
                onTap: () => Navigator.pop(context),
                child: CrrNeumorphic93.container(
                  width: 44,
                  height: 44,
                  borderRadius: 22,
                  child: const Icon(
                    Icons.arrow_back_ios_new,
                    color: CrrNeumorphic93.textPrimary,
                    size: 20,
                  ),
                ),
              ),
              const Spacer(),
              Text(
                'Treasure Vault',
                style: TextStyle(
                  color: CrrNeumorphic93.textPrimary,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              const SizedBox(width: 44), // 平衡布局
            ],
          ),
          
          const SizedBox(height: 20),
          
          // 当前金币余额
          Consumer<CrrCoinManager78>(
            builder: (context, coinManager, child) {
              return CrrNeumorphic93.container(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                borderRadius: 20,
                backgroundColor: CrrNeumorphic93.primaryColor.withOpacity(0.1),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: CrrNeumorphic93.primaryColor,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Icon(
                        Icons.monetization_on,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      coinManager.formattedBalance,
                      style: TextStyle(
                        color: CrrNeumorphic93.textPrimary,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Coins',
                      style: TextStyle(
                        color: CrrNeumorphic93.textSecondary,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildGoodsGrid() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.8,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: _goodsList.length,
        itemBuilder: (context, index) {
          final goods = _goodsList[index];
          return _buildGoodsCard(goods, index);
        },
      ),
    );
  }

  Widget _buildGoodsCard(CrrSCGoods goods, int index) {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, (1 - _fadeAnimation.value) * 50),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: GestureDetector(
              onTap: () => _handlePurchase(goods),
              child: CrrNeumorphic93.card(
                padding: const EdgeInsets.all(20),
                borderRadius: 24,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // 金币图标和数量
                    Column(
                      children: [
                        Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                CrrNeumorphic93.primaryColor,
                                CrrNeumorphic93.primaryColor.withOpacity(0.7),
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(30),
                            boxShadow: [
                              BoxShadow(
                                color: CrrNeumorphic93.primaryColor.withOpacity(0.3),
                                blurRadius: 12,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.monetization_on,
                            color: Colors.white,
                            size: 32,
                          ),
                        ),
                        
                        const SizedBox(height: 12),
                        
                        Text(
                          goods.formattedCoin,
                          style: TextStyle(
                            color: CrrNeumorphic93.textPrimary,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                    
                    // 标签（如果有）
                    if (goods.hasTag)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: CrrNeumorphic93.primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          goods.tags,
                          style: TextStyle(
                            color: CrrNeumorphic93.primaryColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    
                    // 价格和购买按钮
                    Column(
                      children: [
                        Text(
                          goods.formattedPrice,
                          style: TextStyle(
                            color: CrrNeumorphic93.textSecondary,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        
                        const SizedBox(height: 12),
                        
                        CrrNeumorphic93.container(
                          width: double.infinity,
                          height: 40,
                          borderRadius: 20,
                          backgroundColor: CrrNeumorphic93.primaryColor,
                          child: Center(
                            child: Text(
                              'Purchase',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.black.withOpacity(0.5),
      child: Center(
        child: CrrNeumorphic93.container(
          width: 120,
          height: 120,
          borderRadius: 24,
          backgroundColor: CrrNeumorphic93.cardColor,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 40,
                height: 40,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    CrrNeumorphic93.primaryColor,
                  ),
                  strokeWidth: 3,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Processing...',
                style: TextStyle(
                  color: CrrNeumorphic93.textPrimary,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handlePurchase(CrrSCGoods goods) {
    if (_isLoading) return;
    
    CrrPurchaseManager91.instance.purchaseProduct(goods.code);
  }

  void _showSuccessDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: CrrNeumorphic93.cardColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 28),
            const SizedBox(width: 12),
            Text(
              'Success',
              style: TextStyle(
                color: CrrNeumorphic93.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Text(
          message,
          style: TextStyle(color: CrrNeumorphic93.textSecondary),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'OK',
              style: TextStyle(color: CrrNeumorphic93.primaryColor),
            ),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: CrrNeumorphic93.cardColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Icon(Icons.error, color: Colors.red, size: 28),
            const SizedBox(width: 12),
            Text(
              'Error',
              style: TextStyle(
                color: CrrNeumorphic93.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Text(
          message,
          style: TextStyle(color: CrrNeumorphic93.textSecondary),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'OK',
              style: TextStyle(color: CrrNeumorphic93.primaryColor),
            ),
          ),
        ],
      ),
    );
  }

  void _showCancelDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: CrrNeumorphic93.cardColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Icon(Icons.info, color: Colors.orange, size: 28),
            const SizedBox(width: 12),
            Text(
              'Cancelled',
              style: TextStyle(
                color: CrrNeumorphic93.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Text(
          message,
          style: TextStyle(color: CrrNeumorphic93.textSecondary),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'OK',
              style: TextStyle(color: CrrNeumorphic93.primaryColor),
            ),
          ),
        ],
      ),
    );
  }
}
