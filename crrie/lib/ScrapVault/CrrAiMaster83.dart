/// AI角色数据模型
/// 定义废品利用创意指导师的核心属性和行为
class CrrAiMaster83 {
  final String masterId;
  final String name;
  final String englishId;
  final String shortDescription;
  final String longDescription;
  final String avatarPath;
  final String category;
  final List<String> specialties;
  final List<String> exampleQuestions;
  final int freeChatsRemaining;
  final bool isUnlocked;
  final double rating;
  final int totalChats;

  const CrrAiMaster83({
    required this.masterId,
    required this.name,
    required this.englishId,
    required this.shortDescription,
    required this.longDescription,
    required this.avatarPath,
    required this.category,
    required this.specialties,
    this.exampleQuestions = const [],
    this.freeChatsRemaining = 3,
    this.isUnlocked = true,
    this.rating = 5.0,
    this.totalChats = 0,
  });

  /// 从JSON创建实例
  factory CrrAiMaster83.fromJson(Map<String, dynamic> json) {
    return CrrAiMaster83(
      masterId: json['masterId'] ?? '',
      name: json['name'] ?? '',
      englishId: json['englishId'] ?? '',
      shortDescription: json['shortDescription'] ?? '',
      longDescription: json['longDescription'] ?? '',
      avatarPath: json['avatarPath'] ?? '',
      category: json['category'] ?? '',
      specialties: List<String>.from(json['specialties'] ?? []),
      exampleQuestions: List<String>.from(json['exampleQuestions'] ?? []),
      freeChatsRemaining: json['freeChatsRemaining'] ?? 3,
      isUnlocked: json['isUnlocked'] ?? true,
      rating: (json['rating'] ?? 5.0).toDouble(),
      totalChats: json['totalChats'] ?? 0,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'masterId': masterId,
      'name': name,
      'englishId': englishId,
      'shortDescription': shortDescription,
      'longDescription': longDescription,
      'avatarPath': avatarPath,
      'category': category,
      'specialties': specialties,
      'exampleQuestions': exampleQuestions,
      'freeChatsRemaining': freeChatsRemaining,
      'isUnlocked': isUnlocked,
      'rating': rating,
      'totalChats': totalChats,
    };
  }

  /// 复制并修改属性
  CrrAiMaster83 copyWith({
    String? masterId,
    String? name,
    String? englishId,
    String? shortDescription,
    String? longDescription,
    String? avatarPath,
    String? category,
    List<String>? specialties,
    List<String>? exampleQuestions,
    int? freeChatsRemaining,
    bool? isUnlocked,
    double? rating,
    int? totalChats,
  }) {
    return CrrAiMaster83(
      masterId: masterId ?? this.masterId,
      name: name ?? this.name,
      englishId: englishId ?? this.englishId,
      shortDescription: shortDescription ?? this.shortDescription,
      longDescription: longDescription ?? this.longDescription,
      avatarPath: avatarPath ?? this.avatarPath,
      category: category ?? this.category,
      specialties: specialties ?? this.specialties,
      exampleQuestions: exampleQuestions ?? this.exampleQuestions,
      freeChatsRemaining: freeChatsRemaining ?? this.freeChatsRemaining,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      rating: rating ?? this.rating,
      totalChats: totalChats ?? this.totalChats,
    );
  }

  /// 是否可以免费聊天
  bool get canChatForFree => freeChatsRemaining > 0;

  /// 获取专业领域标签
  String get specialtyTags => specialties.join(', ');

  /// 获取详细描述（别名）
  String get detailedDescription => longDescription;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CrrAiMaster83 && other.masterId == masterId;
  }

  @override
  int get hashCode => masterId.hashCode;

  @override
  String toString() {
    return 'CrrAiMaster83(masterId: $masterId, name: $name, englishId: $englishId)';
  }
}

/// AI角色数据仓库
class CrrAiMasterRepo92 {
  /// 获取所有AI角色数据
  static List<CrrAiMaster83> getAllMasters() {
    return [
      CrrAiMaster83(
        masterId: 'master_001',
        name: 'Carton Upcycle Creator',
        englishId: 'CartonUpcycleCreator',
        shortDescription: 'Guide creative reuse of cardboard boxes.',
        longDescription: 'Teach making storage bins (cut, fold, decorate with fabric), kids\' playhouses (cut windows/doors), or wall shelves (reinforce with tape). Suggest painting or wrapping for aesthetic appeal.',
        avatarPath: 'assets/ai/ai1.png',
        category: 'Paper & Cardboard',
        specialties: ['Storage Solutions', 'Kids Toys', 'Wall Decorations'],
        exampleQuestions: [
          'How can I turn a large cardboard box into a storage bin?',
          'What\'s the best way to make a playhouse from cardboard?',
          'Can you help me create wall shelves from cardboard?',
        ],
      ),
      CrrAiMaster83(
        masterId: 'master_002',
        name: 'Plastic Bottle Reuse Advisor',
        englishId: 'PlasticBottleReuseAdvisor',
        shortDescription: 'Instruct on turning plastic bottles into useful items.',
        longDescription: 'Show making planters (cut bottom, add drainage holes), pencil holders (decorate with paint), or bird feeders (fill with seeds, hang outdoors).',
        avatarPath: 'assets/ai/ai2.png',
        category: 'Plastic Materials',
        specialties: ['Garden Planters', 'Office Organizers', 'Bird Feeders'],
        exampleQuestions: [
          'How do I make a planter from a plastic bottle?',
          'Can you show me how to create a bird feeder?',
          'What\'s the best way to make pencil holders from bottles?',
        ],
      ),
      CrrAiMaster83(
        masterId: 'master_003',
        name: 'Glass Jar Upcycle Designer',
        englishId: 'GlassJarUpcycleDesigner',
        shortDescription: 'Create ideas for repurposing glass jars.',
        longDescription: 'Recommend candle holders (add wax/wick), food storage (label with chalkboard paint), or lanterns (wrap with string lights inside). Advise sanding edges for safety.',
        avatarPath: 'assets/ai/ai3.png',
        category: 'Glass & Ceramics',
        specialties: ['Lighting Solutions', 'Storage Containers', 'Decorative Items'],
        exampleQuestions: [
          'How can I turn glass jars into candle holders?',
          'What\'s the best way to make storage containers from jars?',
          'Can you help me create lanterns with string lights?',
        ],
      ),
      CrrAiMaster83(
        masterId: 'master_004',
        name: 'Old Clothes Reuse Instructor',
        englishId: 'OldClothesReuseInstructor',
        shortDescription: 'Teach ways to repurpose worn-out clothes.',
        longDescription: 'Show turning t-shirts into tote bags (cut sleeves/neck, sew bottom), jeans into patchwork pillows (cut fabric, stitch together), or socks into hand warmers (fill with rice).',
        avatarPath: 'assets/ai/ai4.png',
        category: 'Textiles & Fabrics',
        specialties: ['Fashion Accessories', 'Home Textiles', 'Comfort Items'],
        exampleQuestions: [
          'How do I turn old t-shirts into tote bags?',
          'Can you show me how to make pillows from old jeans?',
          'What\'s the process for making hand warmers from socks?',
        ],
      ),
      CrrAiMaster83(
        masterId: 'master_005',
        name: 'Newspaper Magazine Reuse Expert',
        englishId: 'NewspaperMagazineReuseExpert',
        shortDescription: 'Offer ideas for reusing old papers and magazines.',
        longDescription: 'Suggest paper mache bowls (rip into strips, mix with glue), gift wrap (decorate with stamps), or collage art (cut images, arrange on canvas).',
        avatarPath: 'assets/ai/ai5.png',
        category: 'Paper & Print',
        specialties: ['Art Projects', 'Gift Wrapping', 'Decorative Crafts'],
        exampleQuestions: [
          'How can I make paper mache bowls from old newspapers?',
          'What\'s the best way to use magazines as gift wrap?',
          'Can you help me create collage art from magazine cutouts?',
        ],
      ),
      CrrAiMaster83(
        masterId: 'master_006',
        name: 'Bottle Cap Upcycle Creator',
        englishId: 'BottleCapUpcycleCreator',
        shortDescription: 'Design projects using plastic/metal bottle caps.',
        longDescription: 'Create coasters (glue caps to a base), fridge magnets (add magnet strips), or mosaic art (arrange caps in patterns on trays).',
        avatarPath: 'assets/ai/ai6.png',
        category: 'Small Components',
        specialties: ['Home Accessories', 'Art Projects', 'Functional Items'],
        exampleQuestions: [
          'How do I make coasters from bottle caps?',
          'Can you show me how to create fridge magnets?',
          'What\'s the process for making mosaic art with caps?',
        ],
      ),
      CrrAiMaster83(
        masterId: 'master_007',
        name: 'Old Furniture Upcycle Advisor',
        englishId: 'OldFurnitureUpcycleAdvisor',
        shortDescription: 'Guide repurposing outdated or damaged furniture.',
        longDescription: 'Teach painting dressers with chalk paint, turning wooden crates into shelves (stack and secure), or transforming chairs into plant stands (remove seat, add planter).',
        avatarPath: 'assets/ai/ai7.png',
        category: 'Furniture & Wood',
        specialties: ['Furniture Restoration', 'Storage Solutions', 'Garden Furniture'],
        exampleQuestions: [
          'How can I restore old wooden furniture with chalk paint?',
          'What\'s the best way to turn wooden crates into shelves?',
          'Can you help me transform chairs into plant stands?',
        ],
      ),
      CrrAiMaster83(
        masterId: 'master_008',
        name: 'Battery Waste Reuse Instructor',
        englishId: 'BatteryWasteReuseInstructor',
        shortDescription: 'Offer safe ways to reuse or dispose of old batteries.',
        longDescription: 'Suggest using dead AA batteries as weights in crafts, or creating a battery recycling station. Emphasize proper disposal to avoid environmental harm.',
        avatarPath: 'assets/ai/ai8.png',
        category: 'Electronic Components',
        specialties: ['Safe Disposal', 'Craft Weights', 'Environmental Safety'],
        exampleQuestions: [
          'How can I safely dispose of old batteries?',
          'What are some safe ways to reuse dead batteries?',
          'Can you help me set up a battery recycling station?',
        ],
      ),
      CrrAiMaster83(
        masterId: 'master_009',
        name: 'Fabric Scrap Reuse Expert',
        englishId: 'FabricScrapReuseExpert',
        shortDescription: 'Create ideas for using leftover fabric pieces.',
        longDescription: 'Make fabric scrap garlands (tie strips to string), patchwork quilts (sew small pieces), or stuffed toys (cut shapes, stuff and stitch).',
        avatarPath: 'assets/ai/ai9.png',
        category: 'Fabric Scraps',
        specialties: ['Decorative Items', 'Quilting', 'Toy Making'],
        exampleQuestions: [
          'How do I make garlands from fabric scraps?',
          'Can you show me how to create a patchwork quilt?',
          'What\'s the process for making stuffed toys from scraps?',
        ],
      ),
      CrrAiMaster83(
        masterId: 'master_010',
        name: 'Electronic Waste Upcycle Designer',
        englishId: 'ElectronicWasteUpcycleDesigner',
        shortDescription: 'Design projects using old electronic parts.',
        longDescription: 'Turn circuit boards into coasters (seal with resin), old keyboards into wall art (arrange keys creatively), or phone cases into small storage boxes.',
        avatarPath: 'assets/ai/ai10.png',
        category: 'Electronics',
        specialties: ['Tech Art', 'Storage Solutions', 'Decorative Items'],
        exampleQuestions: [
          'How can I turn circuit boards into decorative coasters?',
          'What\'s the best way to create wall art from old keyboards?',
          'Can you help me make storage boxes from phone cases?',
        ],
      ),
    ];
  }

  /// 根据ID获取角色
  static CrrAiMaster83? getMasterById(String masterId) {
    try {
      return getAllMasters().firstWhere((master) => master.masterId == masterId);
    } catch (e) {
      return null;
    }
  }

  /// 根据分类获取角色
  static List<CrrAiMaster83> getMastersByCategory(String category) {
    return getAllMasters().where((master) => master.category == category).toList();
  }

  /// 获取所有分类
  static List<String> getAllCategories() {
    return getAllMasters().map((master) => master.category).toSet().toList();
  }
}
