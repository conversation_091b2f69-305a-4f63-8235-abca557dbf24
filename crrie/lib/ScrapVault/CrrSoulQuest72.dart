import 'dart:math';

/// 每日启发问题数据模型
/// 管理每日创意启发系统的问题库和奖励机制
class CrrSoulQuest72 {
  final String questionId;
  final String questionText;
  final String category;
  final int rewardCoins;
  final QuestionDifficulty difficulty;
  final List<String> tags;
  final bool isActive;

  const CrrSoulQuest72({
    required this.questionId,
    required this.questionText,
    required this.category,
    this.rewardCoins = 10,
    this.difficulty = QuestionDifficulty.medium,
    this.tags = const [],
    this.isActive = true,
  });

  /// 从JSON创建实例
  factory CrrSoulQuest72.fromJson(Map<String, dynamic> json) {
    return CrrSoulQuest72(
      questionId: json['questionId'] ?? '',
      questionText: json['questionText'] ?? '',
      category: json['category'] ?? '',
      rewardCoins: json['rewardCoins'] ?? 10,
      difficulty: QuestionDifficulty.values.firstWhere(
        (d) => d.name == json['difficulty'],
        orElse: () => QuestionDifficulty.medium,
      ),
      tags: List<String>.from(json['tags'] ?? []),
      isActive: json['isActive'] ?? true,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'questionId': questionId,
      'questionText': questionText,
      'category': category,
      'rewardCoins': rewardCoins,
      'difficulty': difficulty.name,
      'tags': tags,
      'isActive': isActive,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CrrSoulQuest72 && other.questionId == questionId;
  }

  @override
  int get hashCode => questionId.hashCode;
}

/// 用户回答记录
class CrrSoulAnswer84 {
  final String answerId;
  final String questionId;
  final String userId;
  final String answerText;
  final DateTime answeredAt;
  final int coinsEarned;
  final bool isRewarded;

  const CrrSoulAnswer84({
    required this.answerId,
    required this.questionId,
    required this.userId,
    required this.answerText,
    required this.answeredAt,
    required this.coinsEarned,
    this.isRewarded = false,
  });

  factory CrrSoulAnswer84.fromJson(Map<String, dynamic> json) {
    return CrrSoulAnswer84(
      answerId: json['answerId'] ?? '',
      questionId: json['questionId'] ?? '',
      userId: json['userId'] ?? '',
      answerText: json['answerText'] ?? '',
      answeredAt: DateTime.parse(
        json['answeredAt'] ?? DateTime.now().toIso8601String(),
      ),
      coinsEarned: json['coinsEarned'] ?? 0,
      isRewarded: json['isRewarded'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'answerId': answerId,
      'questionId': questionId,
      'userId': userId,
      'answerText': answerText,
      'answeredAt': answeredAt.toIso8601String(),
      'coinsEarned': coinsEarned,
      'isRewarded': isRewarded,
    };
  }
}

/// 问题难度枚举
enum QuestionDifficulty {
  easy,    // 简单 - 5金币
  medium,  // 中等 - 10金币
  hard,    // 困难 - 15金币
}

/// 每日启发问题仓库
class CrrSoulQuestRepo95 {
  /// 获取所有启发问题
  static List<CrrSoulQuest72> getAllQuestions() {
    return [
      // 环保意识类问题
      const CrrSoulQuest72(
        questionId: 'soul_001',
        questionText: 'What small change in your daily routine could make the biggest environmental impact?',
        category: 'Environmental Awareness',
        rewardCoins: 10,
        difficulty: QuestionDifficulty.medium,
        tags: ['environment', 'daily_life', 'impact'],
      ),
      const CrrSoulQuest72(
        questionId: 'soul_002',
        questionText: 'If you could transform one piece of waste in your home right now, what would it be and why?',
        category: 'Creative Thinking',
        rewardCoins: 10,
        difficulty: QuestionDifficulty.medium,
        tags: ['creativity', 'waste', 'transformation'],
      ),
      const CrrSoulQuest72(
        questionId: 'soul_003',
        questionText: 'What childhood memory do you have of creating something from nothing?',
        category: 'Personal Reflection',
        rewardCoins: 10,
        difficulty: QuestionDifficulty.easy,
        tags: ['childhood', 'creativity', 'memory'],
      ),
      const CrrSoulQuest72(
        questionId: 'soul_004',
        questionText: 'How do you feel when you see perfectly good items being thrown away?',
        category: 'Emotional Awareness',
        rewardCoins: 10,
        difficulty: QuestionDifficulty.medium,
        tags: ['emotions', 'waste', 'awareness'],
      ),
      const CrrSoulQuest72(
        questionId: 'soul_005',
        questionText: 'What would you teach a child about the value of reusing materials?',
        category: 'Teaching & Values',
        rewardCoins: 15,
        difficulty: QuestionDifficulty.hard,
        tags: ['education', 'values', 'children'],
      ),
      
      // 创意思维类问题
      const CrrSoulQuest72(
        questionId: 'soul_006',
        questionText: 'If materials could speak, what would your old clothes say about their journey?',
        category: 'Creative Imagination',
        rewardCoins: 10,
        difficulty: QuestionDifficulty.medium,
        tags: ['imagination', 'clothes', 'storytelling'],
      ),
      const CrrSoulQuest72(
        questionId: 'soul_007',
        questionText: 'What\'s the most unexpected use you\'ve found for a common household item?',
        category: 'Innovation',
        rewardCoins: 10,
        difficulty: QuestionDifficulty.easy,
        tags: ['innovation', 'household', 'unexpected'],
      ),
      const CrrSoulQuest72(
        questionId: 'soul_008',
        questionText: 'How does creating something with your hands make you feel different than buying it?',
        category: 'Mindfulness',
        rewardCoins: 15,
        difficulty: QuestionDifficulty.hard,
        tags: ['mindfulness', 'creation', 'emotions'],
      ),
      const CrrSoulQuest72(
        questionId: 'soul_009',
        questionText: 'What story would you tell about a plastic bottle\'s second life?',
        category: 'Storytelling',
        rewardCoins: 10,
        difficulty: QuestionDifficulty.medium,
        tags: ['storytelling', 'plastic', 'second_life'],
      ),
      const CrrSoulQuest72(
        questionId: 'soul_010',
        questionText: 'If you had to live with only items you\'ve upcycled for a week, what would you create first?',
        category: 'Practical Thinking',
        rewardCoins: 15,
        difficulty: QuestionDifficulty.hard,
        tags: ['practical', 'upcycling', 'priority'],
      ),
      
      // 生活哲学类问题
      const CrrSoulQuest72(
        questionId: 'soul_011',
        questionText: 'What does \'enough\' mean to you in a world of endless consumption?',
        category: 'Life Philosophy',
        rewardCoins: 15,
        difficulty: QuestionDifficulty.hard,
        tags: ['philosophy', 'consumption', 'enough'],
      ),
      const CrrSoulQuest72(
        questionId: 'soul_012',
        questionText: 'How has your relationship with \'stuff\' changed as you\'ve grown older?',
        category: 'Personal Growth',
        rewardCoins: 10,
        difficulty: QuestionDifficulty.medium,
        tags: ['growth', 'materialism', 'age'],
      ),
      const CrrSoulQuest72(
        questionId: 'soul_013',
        questionText: 'What would future generations thank you for doing today?',
        category: 'Future Thinking',
        rewardCoins: 15,
        difficulty: QuestionDifficulty.hard,
        tags: ['future', 'legacy', 'responsibility'],
      ),
      const CrrSoulQuest72(
        questionId: 'soul_014',
        questionText: 'When did you last feel truly proud of something you made yourself?',
        category: 'Achievement',
        rewardCoins: 10,
        difficulty: QuestionDifficulty.easy,
        tags: ['pride', 'achievement', 'creation'],
      ),
      const CrrSoulQuest72(
        questionId: 'soul_015',
        questionText: 'What would you do if new things stopped being made tomorrow?',
        category: 'Hypothetical Thinking',
        rewardCoins: 15,
        difficulty: QuestionDifficulty.hard,
        tags: ['hypothetical', 'scarcity', 'adaptation'],
      ),
      
      // 社区与分享类问题
      const CrrSoulQuest72(
        questionId: 'soul_016',
        questionText: 'How could your neighborhood become more sustainable through sharing?',
        category: 'Community',
        rewardCoins: 10,
        difficulty: QuestionDifficulty.medium,
        tags: ['community', 'sharing', 'sustainability'],
      ),
      const CrrSoulQuest72(
        questionId: 'soul_017',
        questionText: 'What skill would you love to learn from someone who\'s great at upcycling?',
        category: 'Learning',
        rewardCoins: 10,
        difficulty: QuestionDifficulty.easy,
        tags: ['learning', 'skills', 'upcycling'],
      ),
      const CrrSoulQuest72(
        questionId: 'soul_018',
        questionText: 'How do you decide what deserves a second chance in life?',
        category: 'Decision Making',
        rewardCoins: 15,
        difficulty: QuestionDifficulty.hard,
        tags: ['decisions', 'second_chances', 'value'],
      ),
      const CrrSoulQuest72(
        questionId: 'soul_019',
        questionText: 'What would you put in a time capsule to show how people reused things in 2025?',
        category: 'Time & History',
        rewardCoins: 10,
        difficulty: QuestionDifficulty.medium,
        tags: ['time_capsule', 'history', 'reuse'],
      ),
      const CrrSoulQuest72(
        questionId: 'soul_020',
        questionText: 'If you could give one piece of advice to someone just starting their eco-journey, what would it be?',
        category: 'Wisdom Sharing',
        rewardCoins: 10,
        difficulty: QuestionDifficulty.medium,
        tags: ['advice', 'eco_journey', 'wisdom'],
      ),
    ];
  }

  /// 获取今日随机问题
  static CrrSoulQuest72 getTodayQuestion() {
    final questions = getAllQuestions();
    final today = DateTime.now();
    final seed = today.year * 10000 + today.month * 100 + today.day;
    final random = Random(seed);
    
    return questions[random.nextInt(questions.length)];
  }

  /// 根据分类获取问题
  static List<CrrSoulQuest72> getQuestionsByCategory(String category) {
    return getAllQuestions()
        .where((q) => q.category == category)
        .toList();
  }

  /// 获取所有分类
  static List<String> getAllCategories() {
    return getAllQuestions()
        .map((q) => q.category)
        .toSet()
        .toList();
  }

  /// 根据难度获取问题
  static List<CrrSoulQuest72> getQuestionsByDifficulty(QuestionDifficulty difficulty) {
    return getAllQuestions()
        .where((q) => q.difficulty == difficulty)
        .toList();
  }
}
