import 'package:uuid/uuid.dart';

/// 用户信息数据模型
/// 管理用户基本信息、金币、使用统计等
class CrrUserModel47 {
  final String userId;
  final String nickname;
  final String avatarPath;
  final int totalCoins;
  final DateTime lastSoulQuestionDate;
  final DateTime createdAt;
  final DateTime lastActiveAt;
  final Map<String, int> masterChatCounts;
  final List<String> favoriteMasterIds;
  final bool isFirstLaunch;
  final Map<String, dynamic> preferences;
  final int answeredSoulQuestions;

  const CrrUserModel47({
    required this.userId,
    required this.nickname,
    required this.avatarPath,
    this.totalCoins = 0,
    required this.lastSoulQuestionDate,
    required this.createdAt,
    required this.lastActiveAt,
    this.masterChatCounts = const {},
    this.favoriteMasterIds = const [],
    this.isFirstLaunch = true,
    this.preferences = const {},
    this.answeredSoulQuestions = 0,
  });

  /// 创建新用户
  factory CrrUserModel47.createNew({
    String? nickname,
    String? avatarPath,
  }) {
    final now = DateTime.now();
    return CrrUserModel47(
      userId: const Uuid().v4(),
      nickname: nickname ?? 'Eco Crafter',
      avatarPath: avatarPath ?? 'assets/avatars/avatar_01.jpg',
      totalCoins: 50, // 新用户赠送50金币
      lastSoulQuestionDate: DateTime(2000), // 设置为很早的日期，确保首次可以答题
      createdAt: now,
      lastActiveAt: now,
      masterChatCounts: {},
      favoriteMasterIds: [],
      isFirstLaunch: true,
      answeredSoulQuestions: 0,
      preferences: {
        'theme': 'eco_green',
        'notifications': true,
        'sound_effects': true,
        'haptic_feedback': true,
      },
    );
  }

  /// 从JSON创建实例
  factory CrrUserModel47.fromJson(Map<String, dynamic> json) {
    return CrrUserModel47(
      userId: json['userId'] ?? '',
      nickname: json['nickname'] ?? 'Eco Crafter',
      avatarPath: json['avatarPath'] ?? 'assets/avatars/avatar_01.jpg',
      totalCoins: json['totalCoins'] ?? 0,
      lastSoulQuestionDate: DateTime.parse(
        json['lastSoulQuestionDate'] ?? '2000-01-01T00:00:00.000Z',
      ),
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      lastActiveAt: DateTime.parse(
        json['lastActiveAt'] ?? DateTime.now().toIso8601String(),
      ),
      masterChatCounts: Map<String, int>.from(json['masterChatCounts'] ?? {}),
      favoriteMasterIds: List<String>.from(json['favoriteMasterIds'] ?? []),
      isFirstLaunch: json['isFirstLaunch'] ?? true,
      answeredSoulQuestions: json['answeredSoulQuestions'] ?? 0,
      preferences: Map<String, dynamic>.from(json['preferences'] ?? {}),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'nickname': nickname,
      'avatarPath': avatarPath,
      'totalCoins': totalCoins,
      'lastSoulQuestionDate': lastSoulQuestionDate.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'lastActiveAt': lastActiveAt.toIso8601String(),
      'masterChatCounts': masterChatCounts,
      'favoriteMasterIds': favoriteMasterIds,
      'isFirstLaunch': isFirstLaunch,
      'answeredSoulQuestions': answeredSoulQuestions,
      'preferences': preferences,
    };
  }

  /// 复制并修改属性
  CrrUserModel47 copyWith({
    String? userId,
    String? nickname,
    String? avatarPath,
    int? totalCoins,
    DateTime? lastSoulQuestionDate,
    DateTime? createdAt,
    DateTime? lastActiveAt,
    Map<String, int>? masterChatCounts,
    List<String>? favoriteMasterIds,
    bool? isFirstLaunch,
    int? answeredSoulQuestions,
    Map<String, dynamic>? preferences,
  }) {
    return CrrUserModel47(
      userId: userId ?? this.userId,
      nickname: nickname ?? this.nickname,
      avatarPath: avatarPath ?? this.avatarPath,
      totalCoins: totalCoins ?? this.totalCoins,
      lastSoulQuestionDate: lastSoulQuestionDate ?? this.lastSoulQuestionDate,
      createdAt: createdAt ?? this.createdAt,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
      masterChatCounts: masterChatCounts ?? this.masterChatCounts,
      favoriteMasterIds: favoriteMasterIds ?? this.favoriteMasterIds,
      isFirstLaunch: isFirstLaunch ?? this.isFirstLaunch,
      answeredSoulQuestions: answeredSoulQuestions ?? this.answeredSoulQuestions,
      preferences: preferences ?? this.preferences,
    );
  }

  /// 检查今天是否已经回答过灵魂问题
  bool get canAnswerSoulQuestionToday {
    final today = DateTime.now();
    final lastAnswerDate = lastSoulQuestionDate;
    
    return today.year != lastAnswerDate.year ||
           today.month != lastAnswerDate.month ||
           today.day != lastAnswerDate.day;
  }

  /// 获取与特定AI角色的聊天次数
  int getChatCountWithMaster(String masterId) {
    return masterChatCounts[masterId] ?? 0;
  }

  /// 检查是否收藏了特定AI角色
  bool isMasterFavorited(String masterId) {
    return favoriteMasterIds.contains(masterId);
  }

  /// 获取总聊天次数
  int get totalChatCount {
    return masterChatCounts.values.fold(0, (sum, count) => sum + count);
  }

  /// 获取总聊天会话数（别名）
  int get totalChatSessions => totalChatCount;

  /// 获取收藏的AI角色数量
  int get favoriteCount => favoriteMasterIds.length;

  /// 获取用户等级（基于聊天次数）
  int get userLevel {
    final total = totalChatCount;
    if (total < 10) return 1;
    if (total < 25) return 2;
    if (total < 50) return 3;
    if (total < 100) return 4;
    return 5;
  }

  /// 获取用户等级标题
  String get userLevelTitle {
    switch (userLevel) {
      case 1:
        return 'Eco Beginner';
      case 2:
        return 'Waste Warrior';
      case 3:
        return 'Upcycle Expert';
      case 4:
        return 'Reuse Master';
      case 5:
        return 'Sustainability Champion';
      default:
        return 'Eco Crafter';
    }
  }

  /// 获取下一等级所需聊天次数
  int get chatsToNextLevel {
    final total = totalChatCount;
    switch (userLevel) {
      case 1:
        return 10 - total;
      case 2:
        return 25 - total;
      case 3:
        return 50 - total;
      case 4:
        return 100 - total;
      default:
        return 0;
    }
  }

  /// 获取用户活跃天数
  int get activeDays {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    return difference.inDays + 1;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CrrUserModel47 && other.userId == userId;
  }

  @override
  int get hashCode => userId.hashCode;

  @override
  String toString() {
    return 'CrrUserModel47(userId: $userId, nickname: $nickname, level: $userLevel)';
  }
}

/// 用户偏好设置枚举
enum CrrUserTheme { ecoGreen, oceanBlue, earthBrown, sunsetOrange }

enum CrrNotificationSetting { all, important, none }

/// 用户统计数据
class CrrUserStats64 {
  final int totalChats;
  final int coinsEarned;
  final int soulQuestionsAnswered;
  final int favoriteMasters;
  final int activeDays;
  final Map<String, int> categoryChats;

  const CrrUserStats64({
    required this.totalChats,
    required this.coinsEarned,
    required this.soulQuestionsAnswered,
    required this.favoriteMasters,
    required this.activeDays,
    required this.categoryChats,
  });

  factory CrrUserStats64.fromUser(CrrUserModel47 user) {
    return CrrUserStats64(
      totalChats: user.totalChatCount,
      coinsEarned: user.totalCoins,
      soulQuestionsAnswered: 0, // 需要从其他地方获取
      favoriteMasters: user.favoriteCount,
      activeDays: user.activeDays,
      categoryChats: {}, // 需要从聊天记录计算
    );
  }
}
