import 'package:uuid/uuid.dart';

/// 聊天消息数据模型
/// 存储用户与AI角色的对话记录
class CrrChatMessage91 {
  final String messageId;
  final String chatSessionId;
  final String masterId;
  final String content;
  final bool isFromUser;
  final DateTime timestamp;
  final MessageStatus status;
  final Map<String, dynamic> metadata;

  const CrrChatMessage91({
    required this.messageId,
    required this.chatSessionId,
    required this.masterId,
    required this.content,
    required this.isFromUser,
    required this.timestamp,
    this.status = MessageStatus.sent,
    this.metadata = const {},
  });

  /// 创建用户消息
  factory CrrChatMessage91.createUserMessage({
    required String chatSessionId,
    required String masterId,
    required String content,
  }) {
    return CrrChatMessage91(
      messageId: const Uuid().v4(),
      chatSessionId: chatSessionId,
      masterId: masterId,
      content: content,
      isFromUser: true,
      timestamp: DateTime.now(),
      status: MessageStatus.sent,
    );
  }

  /// 创建AI回复消息
  factory CrrChatMessage91.createAiMessage({
    required String chatSessionId,
    required String masterId,
    required String content,
  }) {
    return CrrChatMessage91(
      messageId: const Uuid().v4(),
      chatSessionId: chatSessionId,
      masterId: masterId,
      content: content,
      isFromUser: false,
      timestamp: DateTime.now(),
      status: MessageStatus.sent,
    );
  }

  /// 从JSON创建实例
  factory CrrChatMessage91.fromJson(Map<String, dynamic> json) {
    return CrrChatMessage91(
      messageId: json['messageId'] ?? '',
      chatSessionId: json['chatSessionId'] ?? '',
      masterId: json['masterId'] ?? '',
      content: json['content'] ?? '',
      isFromUser: json['isFromUser'] ?? false,
      timestamp: DateTime.parse(
        json['timestamp'] ?? DateTime.now().toIso8601String(),
      ),
      status: MessageStatus.values.firstWhere(
        (status) => status.name == json['status'],
        orElse: () => MessageStatus.sent,
      ),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'messageId': messageId,
      'chatSessionId': chatSessionId,
      'masterId': masterId,
      'content': content,
      'isFromUser': isFromUser,
      'timestamp': timestamp.toIso8601String(),
      'status': status.name,
      'metadata': metadata,
    };
  }

  /// 复制并修改属性
  CrrChatMessage91 copyWith({
    String? messageId,
    String? chatSessionId,
    String? masterId,
    String? content,
    bool? isFromUser,
    DateTime? timestamp,
    MessageStatus? status,
    Map<String, dynamic>? metadata,
  }) {
    return CrrChatMessage91(
      messageId: messageId ?? this.messageId,
      chatSessionId: chatSessionId ?? this.chatSessionId,
      masterId: masterId ?? this.masterId,
      content: content ?? this.content,
      isFromUser: isFromUser ?? this.isFromUser,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CrrChatMessage91 && other.messageId == messageId;
  }

  @override
  int get hashCode => messageId.hashCode;
}

/// 聊天会话数据模型
/// 管理与特定AI角色的完整对话会话
class CrrChatSession56 {
  final String sessionId;
  final String masterId;
  final String userId;
  final String title;
  final DateTime createdAt;
  final DateTime lastMessageAt;
  final List<CrrChatMessage91> messages;
  final bool isActive;
  final Map<String, dynamic> sessionData;

  const CrrChatSession56({
    required this.sessionId,
    required this.masterId,
    required this.userId,
    required this.title,
    required this.createdAt,
    required this.lastMessageAt,
    this.messages = const [],
    this.isActive = true,
    this.sessionData = const {},
  });

  /// 创建新会话
  factory CrrChatSession56.createNew({
    required String masterId,
    required String userId,
    String? title,
  }) {
    final now = DateTime.now();
    return CrrChatSession56(
      sessionId: const Uuid().v4(),
      masterId: masterId,
      userId: userId,
      title: title ?? 'Chat with AI Master',
      createdAt: now,
      lastMessageAt: now,
      messages: [],
      isActive: true,
    );
  }

  /// 从JSON创建实例
  factory CrrChatSession56.fromJson(Map<String, dynamic> json) {
    return CrrChatSession56(
      sessionId: json['sessionId'] ?? '',
      masterId: json['masterId'] ?? '',
      userId: json['userId'] ?? '',
      title: json['title'] ?? '',
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      lastMessageAt: DateTime.parse(
        json['lastMessageAt'] ?? DateTime.now().toIso8601String(),
      ),
      messages: (json['messages'] as List<dynamic>?)
              ?.map((msg) => CrrChatMessage91.fromJson(msg))
              .toList() ??
          [],
      isActive: json['isActive'] ?? true,
      sessionData: Map<String, dynamic>.from(json['sessionData'] ?? {}),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'sessionId': sessionId,
      'masterId': masterId,
      'userId': userId,
      'title': title,
      'createdAt': createdAt.toIso8601String(),
      'lastMessageAt': lastMessageAt.toIso8601String(),
      'messages': messages.map((msg) => msg.toJson()).toList(),
      'isActive': isActive,
      'sessionData': sessionData,
    };
  }

  /// 添加消息到会话
  CrrChatSession56 addMessage(CrrChatMessage91 message) {
    final updatedMessages = List<CrrChatMessage91>.from(messages)..add(message);
    return copyWith(
      messages: updatedMessages,
      lastMessageAt: message.timestamp,
    );
  }

  /// 复制并修改属性
  CrrChatSession56 copyWith({
    String? sessionId,
    String? masterId,
    String? userId,
    String? title,
    DateTime? createdAt,
    DateTime? lastMessageAt,
    List<CrrChatMessage91>? messages,
    bool? isActive,
    Map<String, dynamic>? sessionData,
  }) {
    return CrrChatSession56(
      sessionId: sessionId ?? this.sessionId,
      masterId: masterId ?? this.masterId,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      createdAt: createdAt ?? this.createdAt,
      lastMessageAt: lastMessageAt ?? this.lastMessageAt,
      messages: messages ?? this.messages,
      isActive: isActive ?? this.isActive,
      sessionData: sessionData ?? this.sessionData,
    );
  }

  /// 获取消息数量
  int get messageCount => messages.length;

  /// 获取最后一条消息
  CrrChatMessage91? get lastMessage {
    return messages.isNotEmpty ? messages.last : null;
  }

  /// 获取最后一条消息内容预览
  String get lastMessagePreview {
    final last = lastMessage;
    if (last == null) return 'No messages yet';
    
    final content = last.content;
    return content.length > 50 ? '${content.substring(0, 50)}...' : content;
  }

  /// 获取会话持续时间
  Duration get sessionDuration {
    return lastMessageAt.difference(createdAt);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CrrChatSession56 && other.sessionId == sessionId;
  }

  @override
  int get hashCode => sessionId.hashCode;
}

/// 消息状态枚举
enum MessageStatus {
  sending,    // 发送中
  sent,       // 已发送
  delivered,  // 已送达
  failed,     // 发送失败
}

/// 聊天统计数据
class CrrChatStats38 {
  final int totalSessions;
  final int totalMessages;
  final int activeSessions;
  final Map<String, int> masterChatCounts;
  final DateTime? lastChatTime;

  const CrrChatStats38({
    required this.totalSessions,
    required this.totalMessages,
    required this.activeSessions,
    required this.masterChatCounts,
    this.lastChatTime,
  });

  factory CrrChatStats38.empty() {
    return const CrrChatStats38(
      totalSessions: 0,
      totalMessages: 0,
      activeSessions: 0,
      masterChatCounts: {},
    );
  }
}
