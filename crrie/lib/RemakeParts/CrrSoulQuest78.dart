import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../ScrapVault/CrrSoulQuest72.dart';
import '../ReclaimFlow/CrrUserProvider52.dart';
import 'CrrNeumorphic93.dart';

/// AI灵魂提问组件
/// 每日首次使用展示引导弹窗，用户回答后可获得10金币奖励
class CrrSoulQuest78 extends StatefulWidget {
  const CrrSoulQuest78({super.key});

  @override
  State<CrrSoulQuest78> createState() => _CrrSoulQuest78State();
}

class _CrrSoulQuest78State extends State<CrrSoulQuest78>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _expandController;
  bool _isExpanded = false;
  bool _showFirstTimeDialog = false;
  final TextEditingController _answerController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _expandController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _checkFirstTime();
  }

  void _checkFirstTime() {
    // 检查是否首次使用灵魂提问功能
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final userProvider = context.read<CrrUserProvider52>();
      final user = userProvider.currentUser;
      
      if (user != null && user.isFirstLaunch) {
        setState(() {
          _showFirstTimeDialog = true;
        });
        _showFirstTimeGuide();
      }
    });
  }

  void _showFirstTimeGuide() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _buildFirstTimeDialog(),
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _expandController.dispose();
    _answerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CrrUserProvider52>(
      builder: (context, userProvider, child) {
        final user = userProvider.currentUser;
        if (user == null || !user.canAnswerSoulQuestionToday) {
          return const SizedBox.shrink();
        }

        return AnimatedContainer(
          duration: const Duration(milliseconds: 400),
          height: _isExpanded ? 280 : 80,
          child: CrrNeumorphic93.card(
            padding: const EdgeInsets.all(20),
            borderRadius: 20,
            child: _isExpanded ? _buildExpandedContent() : _buildCollapsedContent(),
          ),
        );
      },
    );
  }

  /// 构建折叠状态内容
  Widget _buildCollapsedContent() {
    return GestureDetector(
      onTap: _toggleExpanded,
      child: Row(
        children: [
          // 脉动图标
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: CrrNeumorphic93.primaryColor,
              boxShadow: CrrNeumorphic93.getElevatedShadow(),
            ),
            child: const Icon(
              Icons.psychology,
              color: Colors.white,
              size: 24,
            ),
          ).animate(controller: _pulseController)
            .scale(
              begin: const Offset(1.0, 1.0),
              end: const Offset(1.1, 1.1),
              curve: Curves.easeInOut,
            ),
          
          const SizedBox(width: 16),
          
          // 文字内容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Daily Soul Question',
                  style: TextStyle(
                    color: CrrNeumorphic93.textPrimary,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Answer today\'s question and earn 10 coins!',
                  style: TextStyle(
                    color: CrrNeumorphic93.textSecondary,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          
          // 金币图标
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: CrrNeumorphic93.accentColor,
              borderRadius: BorderRadius.circular(12),
              boxShadow: CrrNeumorphic93.getElevatedShadow(),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.monetization_on,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 4),
                const Text(
                  '+10',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(width: 8),
          
          // 展开箭头
          Icon(
            Icons.keyboard_arrow_down,
            color: CrrNeumorphic93.textSecondary,
          ),
        ],
      ),
    );
  }

  /// 构建展开状态内容
  Widget _buildExpandedContent() {
    final todayQuestion = CrrSoulQuestRepo95.getTodayQuestion();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 头部
        Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: CrrNeumorphic93.primaryColor,
                boxShadow: CrrNeumorphic93.getElevatedShadow(),
              ),
              child: const Icon(
                Icons.psychology,
                color: Colors.white,
                size: 24,
              ),
            ),
            
            const SizedBox(width: 16),
            
            Expanded(
              child: Text(
                'Daily Soul Question',
                style: TextStyle(
                  color: CrrNeumorphic93.textPrimary,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
            ),
            
            GestureDetector(
              onTap: _toggleExpanded,
              child: Icon(
                Icons.keyboard_arrow_up,
                color: CrrNeumorphic93.textSecondary,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // 问题内容
        CrrNeumorphic93.container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          borderRadius: 16,
          isInset: true,
          child: Text(
            todayQuestion.questionText,
            style: TextStyle(
              color: CrrNeumorphic93.textPrimary,
              fontSize: 16,
              fontWeight: FontWeight.w500,
              height: 1.4,
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // 回答输入框
        CrrNeumorphic93.textField(
          controller: _answerController,
          hintText: 'Share your thoughts...',
          maxLines: 3,
          borderRadius: 16,
        ),
        
        const SizedBox(height: 16),
        
        // 提交按钮
        CrrNeumorphic93.button(
          onPressed: _submitAnswer,
          height: 48,
          borderRadius: 16,
          backgroundColor: CrrNeumorphic93.primaryColor,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.send,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Submit & Earn 10 Coins',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建首次使用引导弹窗
  Widget _buildFirstTimeDialog() {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: CrrNeumorphic93.card(
        padding: const EdgeInsets.all(24),
        borderRadius: 24,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 图标
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: CrrNeumorphic93.primaryColor,
                boxShadow: CrrNeumorphic93.getElevatedShadow(),
              ),
              child: const Icon(
                Icons.psychology,
                color: Colors.white,
                size: 40,
              ),
            ).animate()
              .scale(duration: 600.ms, curve: Curves.elasticOut),
            
            const SizedBox(height: 20),
            
            // 标题
            Text(
              'Welcome to Soul Questions!',
              style: TextStyle(
                color: CrrNeumorphic93.textPrimary,
                fontWeight: FontWeight.bold,
                fontSize: 20,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 12),
            
            // 说明
            Text(
              'Every day, you can answer a thoughtful question and earn 10 coins. These questions help you reflect on your eco-journey and creative mindset.',
              style: TextStyle(
                color: CrrNeumorphic93.textSecondary,
                fontSize: 16,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 24),
            
            // 按钮
            CrrNeumorphic93.button(
              onPressed: () {
                Navigator.of(context).pop();
                context.read<CrrUserProvider52>().markFirstLaunchComplete();
              },
              height: 48,
              borderRadius: 16,
              backgroundColor: CrrNeumorphic93.primaryColor,
              child: const Text(
                'Got it!',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 切换展开状态
  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
    
    if (_isExpanded) {
      _expandController.forward();
    } else {
      _expandController.reverse();
    }
  }

  /// 提交回答
  void _submitAnswer() async {
    if (_answerController.text.trim().isEmpty) {
      _showMessage('Please write your answer first.');
      return;
    }

    final userProvider = context.read<CrrUserProvider52>();
    final todayQuestion = CrrSoulQuestRepo95.getTodayQuestion();
    
    final success = await userProvider.answerSoulQuestion(
      todayQuestion,
      _answerController.text.trim(),
    );

    if (success) {
      _showMessage('Great! You earned 10 coins! 🎉');
      _answerController.clear();
      setState(() {
        _isExpanded = false;
      });
    } else {
      _showMessage('Something went wrong. Please try again.');
    }
  }

  /// 显示消息
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: CrrNeumorphic93.primaryColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
