import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'CrrNeumorphic93.dart';
import '../CraftAtelier/CrrAiMaster67Hub.dart';
import '../CraftAtelier/CrrProfileCenter89.dart';
import '../CraftAtelier/CrrSoulQuestPage45.dart';
import '../CraftAtelier/CrrConversationArchive67.dart';
import '../CraftAtelier/CrrFavoriteGallery84.dart';

/// 新拟物化悬浮底部导航栏
/// 包含主页、聊天历史、灵魂拷问、个人中心4个tab
class CrrFloatingNav42 extends StatefulWidget {
  final int currentIndex;
  final Function(int) onTap;

  const CrrFloatingNav42({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  State<CrrFloatingNav42> createState() => _CrrFloatingNav42State();
}

class _CrrFloatingNav42State extends State<CrrFloatingNav42>
    with TickerProviderStateMixin {
  late AnimationController _animController;
  late List<AnimationController> _iconControllers;

  final List<NavItem> _navItems = [
    NavItem(
      icon: Icons.home_rounded,
      label: 'Home',
      activeColor: CrrNeumorphic93.primaryColor,
    ),
    NavItem(
      icon: Icons.forum_rounded, // 对话气泡交互图标
      label: 'Chats',
      activeColor: CrrNeumorphic93.accentColor,
    ),
    NavItem(
      icon: Icons.auto_awesome_rounded, // 更美观的灵魂拷问图标
      label: 'Soul Quest',
      activeColor: Colors.purple,
    ),
    NavItem(
      icon: Icons.account_circle_rounded, // 更现代的个人中心图标
      label: 'Profile',
      activeColor: Colors.blue,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _iconControllers = List.generate(
      _navItems.length,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 200),
        vsync: this,
      ),
    );

    _animController.forward();
  }

  @override
  void dispose() {
    _animController.dispose();
    for (var controller in _iconControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 20,
      right: 20,
      bottom: 30,
      child: CrrNeumorphic93.container(
        height: 70,
        borderRadius: 35,
        backgroundColor: CrrNeumorphic93.cardColor,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(
            _navItems.length,
            (index) => _buildNavItem(index),
          ),
        ),
      ).animate(controller: _animController)
        .slideY(begin: 1.0, end: 0.0, curve: Curves.elasticOut)
        .fadeIn(),
    );
  }

  /// 构建导航项
  Widget _buildNavItem(int index) {
    final item = _navItems[index];
    final isSelected = widget.currentIndex == index;
    
    return GestureDetector(
      onTap: () {
        widget.onTap(index);
        _animateIcon(index);
      },
      child: Container(
        width: 60,
        height: 60,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 图标容器
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              width: isSelected ? 40 : 32,
              height: isSelected ? 40 : 32,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isSelected ? item.activeColor : Colors.transparent,
                boxShadow: isSelected 
                    ? CrrNeumorphic93.getElevatedShadow()
                    : null,
              ),
              child: Icon(
                item.icon,
                color: isSelected 
                    ? Colors.white 
                    : CrrNeumorphic93.textSecondary,
                size: isSelected ? 22 : 20,
              ),
            ).animate(controller: _iconControllers[index])
              .scale(
                begin: const Offset(1.0, 1.0),
                end: const Offset(1.2, 1.2),
                duration: 100.ms,
              ),
            
            const SizedBox(height: 4),
            
            // 标签文字
            AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 300),
              style: TextStyle(
                fontSize: isSelected ? 10 : 9,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected 
                    ? item.activeColor 
                    : CrrNeumorphic93.textSecondary,
              ),
              child: Text(item.label),
            ),
          ],
        ),
      ),
    );
  }

  /// 图标点击动画
  void _animateIcon(int index) {
    _iconControllers[index].forward().then((_) {
      _iconControllers[index].reverse();
    });
  }
}

/// 导航项数据模型
class NavItem {
  final IconData icon;
  final String label;
  final Color activeColor;

  NavItem({
    required this.icon,
    required this.label,
    required this.activeColor,
  });
}

/// 带悬浮导航栏的主容器
class CrrMainContainer extends StatefulWidget {
  const CrrMainContainer({super.key});

  @override
  State<CrrMainContainer> createState() => _CrrMainContainerState();
}

class _CrrMainContainerState extends State<CrrMainContainer> {
  int _currentIndex = 0;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CrrNeumorphic93.backgroundColor,
      body: Stack(
        children: [
          // 页面内容
          PageView(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            children: [
              _buildHomePage(),
              _buildChatsPage(),
              _buildSoulQuestPage(),
              _buildProfilePage(),
            ],
          ),
          
          // 悬浮导航栏
          CrrFloatingNav42(
            currentIndex: _currentIndex,
            onTap: (index) {
              setState(() {
                _currentIndex = index;
              });
              _pageController.animateToPage(
                index,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            },
          ),
        ],
      ),
    );
  }

  /// 主页页面
  Widget _buildHomePage() {
    return const CrrAiMaster67Hub();
  }

  /// 聊天历史页面
  Widget _buildChatsPage() {
    return const CrrConversationArchive67();
  }

  /// 灵魂拷问页面
  Widget _buildSoulQuestPage() {
    return const CrrSoulQuestPage45();
  }

  /// 个人中心页面
  Widget _buildProfilePage() {
    return const CrrProfileCenter89();
  }
}
