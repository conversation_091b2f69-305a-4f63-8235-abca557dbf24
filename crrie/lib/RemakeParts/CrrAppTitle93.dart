import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'CrrNeumorphic93.dart';

/// 应用标题组件
/// 统一的3D立体标题样式，用于各个页面的AppBar
class CrrAppTitle93 extends StatelessWidget {
  final String title;
  final AnimationController? animationController;
  final double fontSize;
  final bool showShimmer;

  const CrrAppTitle93({
    super.key,
    required this.title,
    this.animationController,
    this.fontSize = 32,
    this.showShimmer = true,
  });

  @override
  Widget build(BuildContext context) {
    final titleWidget = Container(
      child: Stack(
        children: [
          // 底层阴影 - 深色
          Positioned(
            left: 3,
            top: 3,
            child: Text(
              title,
              style: TextStyle(
                fontSize: fontSize,
                fontWeight: FontWeight.bold,
                color: Colors.black.withOpacity(0.3),
              ),
            ),
          ),
          // 中层阴影 - 中等深度
          Positioned(
            left: 2,
            top: 2,
            child: Text(
              title,
              style: TextStyle(
                fontSize: fontSize,
                fontWeight: FontWeight.bold,
                color: Colors.black.withOpacity(0.2),
              ),
            ),
          ),
          // 顶层文字 - 渐变效果
          ShaderMask(
            shaderCallback: (bounds) => LinearGradient(
              colors: [
                CrrNeumorphic93.primaryColor,
                CrrNeumorphic93.primaryColor.withOpacity(0.8),
                CrrNeumorphic93.accentColor,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ).createShader(bounds),
            child: Text(
              title,
              style: TextStyle(
                fontSize: fontSize,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                shadows: const [
                  Shadow(
                    color: Colors.white,
                    offset: Offset(-1, -1),
                    blurRadius: 2,
                  ),
                  Shadow(
                    color: Colors.black26,
                    offset: Offset(1, 1),
                    blurRadius: 3,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );

    // 如果有动画控制器，添加动画效果
    if (animationController != null) {
      Widget animatedWidget = titleWidget
          .animate(controller: animationController!)
          .fadeIn(duration: 600.ms)
          .slideX(begin: -0.3, end: 0);
      
      if (showShimmer) {
        animatedWidget = animatedWidget
            .then()
            .shimmer(duration: 2000.ms, color: Colors.white.withOpacity(0.3));
      }
      
      return animatedWidget;
    }

    return titleWidget;
  }
}
