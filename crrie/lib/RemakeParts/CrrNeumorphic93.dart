import 'package:flutter/material.dart';

/// 新拟物化设计组件库
/// 提供符合Neumorphism风格的UI组件
class CrrNeumorphic93 {
  // 新拟物化配色方案
  static const Color backgroundColor = Color(0xFFF0F0F3);
  static const Color cardColor = Color(0xFFF0F0F3);
  static const Color primaryColor = Color(0xFF8CC63F);
  static const Color accentColor = Color(0xFFFF8C00);
  static const Color textPrimary = Color(0xFF333333);
  static const Color textSecondary = Color(0xFF666666);
  static const Color shadowDark = Color(0xFFD1D1D6);
  static const Color shadowLight = Color(0xFFFFFFFF);
  static const Color dangerColor = Color(0xFFFF4757);
  static const Color successColor = Color(0xFF2ED573);

  /// 创建新拟物化容器
  static Widget container({
    required Widget child,
    double? width,
    double? height,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double borderRadius = 16,
    bool isPressed = false,
    bool isInset = false,
    Color? backgroundColor,
    VoidCallback? onTap,
  }) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius),
          child: Container(
            padding: padding,
            decoration: BoxDecoration(
              color: backgroundColor ?? cardColor,
              borderRadius: BorderRadius.circular(borderRadius),
              boxShadow: _getNeumorphicShadow(
                isPressed: isPressed,
                isInset: isInset,
              ),
            ),
            child: child,
          ),
        ),
      ),
    );
  }

  /// 创建新拟物化按钮
  static Widget button({
    required Widget child,
    required VoidCallback onPressed,
    double width = double.infinity,
    double height = 56,
    double borderRadius = 16,
    Color? backgroundColor,
    EdgeInsetsGeometry? padding,
    bool isLoading = false,
  }) {
    return StatefulBuilder(
      builder: (context, setState) {
        bool isPressed = false;
        
        return GestureDetector(
          onTapDown: (_) => setState(() => isPressed = true),
          onTapUp: (_) => setState(() => isPressed = false),
          onTapCancel: () => setState(() => isPressed = false),
          onTap: isLoading ? null : onPressed,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 150),
            width: width,
            height: height,
            padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            decoration: BoxDecoration(
              color: backgroundColor ?? primaryColor,
              borderRadius: BorderRadius.circular(borderRadius),
              boxShadow: isPressed 
                ? _getInsetShadow()
                : _getElevatedShadow(),
            ),
            child: Center(
              child: isLoading
                ? SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        backgroundColor == null ? Colors.white : textPrimary,
                      ),
                    ),
                  )
                : child,
            ),
          ),
        );
      },
    );
  }

  /// 创建新拟物化卡片
  static Widget card({
    required Widget child,
    double? width,
    double? height,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double borderRadius = 20,
    Color? backgroundColor,
    VoidCallback? onTap,
    bool isElevated = true,
  }) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius),
          child: Container(
            padding: padding ?? const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: backgroundColor ?? cardColor,
              borderRadius: BorderRadius.circular(borderRadius),
              boxShadow: isElevated 
                ? _getElevatedShadow()
                : _getFlatShadow(),
            ),
            child: child,
          ),
        ),
      ),
    );
  }

  /// 创建新拟物化输入框
  static Widget textField({
    TextEditingController? controller,
    String? hintText,
    String? labelText,
    IconData? prefixIcon,
    IconData? suffixIcon,
    VoidCallback? onSuffixIconTap,
    bool obscureText = false,
    TextInputType? keyboardType,
    TextInputAction? textInputAction,
    Function(String)? onChanged,
    Function(String)? onSubmitted,
    int maxLines = 1,
    double borderRadius = 16,
    EdgeInsetsGeometry? contentPadding,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: _getInsetShadow(),
      ),
      child: TextField(
        controller: controller,
        obscureText: obscureText,
        keyboardType: keyboardType,
        textInputAction: textInputAction,
        onChanged: onChanged,
        onSubmitted: onSubmitted,
        maxLines: maxLines,
        style: const TextStyle(
          color: textPrimary,
          fontSize: 16,
        ),
        decoration: InputDecoration(
          hintText: hintText,
          labelText: labelText,
          hintStyle: const TextStyle(color: textSecondary),
          labelStyle: const TextStyle(color: textSecondary),
          prefixIcon: prefixIcon != null
              ? Icon(prefixIcon, color: textSecondary)
              : null,
          suffixIcon: suffixIcon != null
              ? GestureDetector(
                  onTap: onSuffixIconTap,
                  child: Icon(suffixIcon, color: textSecondary),
                )
              : null,
          border: InputBorder.none,
          contentPadding: contentPadding ?? 
              const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        ),
      ),
    );
  }

  /// 创建新拟物化开关
  static Widget toggle({
    required bool value,
    required Function(bool) onChanged,
    double width = 60,
    double height = 32,
    Color? activeColor,
    Color? inactiveColor,
  }) {
    return GestureDetector(
      onTap: () => onChanged(!value),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: value 
              ? (activeColor ?? primaryColor)
              : (inactiveColor ?? cardColor),
          borderRadius: BorderRadius.circular(height / 2),
          boxShadow: value ? _getElevatedShadow() : _getInsetShadow(),
        ),
        child: AnimatedAlign(
          duration: const Duration(milliseconds: 300),
          alignment: value ? Alignment.centerRight : Alignment.centerLeft,
          child: Container(
            width: height - 4,
            height: height - 4,
            margin: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              boxShadow: _getElevatedShadow(),
            ),
          ),
        ),
      ),
    );
  }

  /// 创建新拟物化进度条
  static Widget progressBar({
    required double progress,
    double width = double.infinity,
    double height = 8,
    Color? backgroundColor,
    Color? progressColor,
    double borderRadius = 4,
  }) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor ?? cardColor,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: _getInsetShadow(),
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: progress.clamp(0.0, 1.0),
        child: Container(
          decoration: BoxDecoration(
            color: progressColor ?? primaryColor,
            borderRadius: BorderRadius.circular(borderRadius),
            boxShadow: _getElevatedShadow(),
          ),
        ),
      ),
    );
  }

  /// 获取新拟物化阴影效果
  static List<BoxShadow> _getNeumorphicShadow({
    bool isPressed = false,
    bool isInset = false,
  }) {
    if (isPressed || isInset) {
      return _getInsetShadow();
    } else {
      return _getElevatedShadow();
    }
  }

  /// 获取凸起阴影效果
  static List<BoxShadow> getElevatedShadow() {
    return [
      const BoxShadow(
        color: shadowDark,
        offset: Offset(8, 8),
        blurRadius: 16,
        spreadRadius: 0,
      ),
      const BoxShadow(
        color: shadowLight,
        offset: Offset(-8, -8),
        blurRadius: 16,
        spreadRadius: 0,
      ),
    ];
  }

  /// 获取凹陷阴影效果
  static List<BoxShadow> getInsetShadow() {
    return [
      const BoxShadow(
        color: shadowDark,
        offset: Offset(4, 4),
        blurRadius: 8,
        spreadRadius: -2,
      ),
      const BoxShadow(
        color: shadowLight,
        offset: Offset(-4, -4),
        blurRadius: 8,
        spreadRadius: -2,
      ),
    ];
  }

  /// 获取平面阴影效果
  static List<BoxShadow> getFlatShadow() {
    return [
      const BoxShadow(
        color: shadowDark,
        offset: Offset(4, 4),
        blurRadius: 12,
        spreadRadius: 0,
      ),
      const BoxShadow(
        color: shadowLight,
        offset: Offset(-4, -4),
        blurRadius: 12,
        spreadRadius: 0,
      ),
    ];
  }

  // 保持私有方法用于内部调用
  static List<BoxShadow> _getElevatedShadow() => getElevatedShadow();
  static List<BoxShadow> _getInsetShadow() => getInsetShadow();
  static List<BoxShadow> _getFlatShadow() => getFlatShadow();
}
