import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../ScrapVault/CrrChatModel29.dart';
import '../ScrapVault/CrrAiMaster83.dart';

/// 对话服务类 - 管理聊天API调用和本地存储
/// 负责与Moonshot AI API交互和聊天记录持久化
class CrrConversationService84 {
  static const String _apiUrl = 'https://api.moonshot.cn/v1/chat/completions';
  static const String _apiKey = 'sk-UjLDXgyVsdh4kNtrLbbu1yFFhuafggJTxEw03ezpnnvXX2fR';
  static const String _storageKey = 'crr_conversation_sessions';
  
  /// 发送消息到AI并获取回复
  Future<String> sendMessageToAi({
    required String userMessage,
    required CrrAiMaster83 master,
    List<CrrChatMessage91> conversationHistory = const [],
  }) async {
    try {
      // 构建对话历史
      final messages = <Map<String, String>>[];
      
      // 添加系统提示
      messages.add({
        'role': 'system',
        'content': _buildSystemPrompt(master),
      });
      
      // 添加历史对话（最近10条）
      final recentHistory = conversationHistory.length > 10 
          ? conversationHistory.sublist(conversationHistory.length - 10)
          : conversationHistory;
          
      for (final msg in recentHistory) {
        messages.add({
          'role': msg.isFromUser ? 'user' : 'assistant',
          'content': msg.content,
        });
      }
      
      // 添加当前用户消息
      messages.add({
        'role': 'user',
        'content': userMessage,
      });
      
      // 发送API请求
      final response = await http.post(
        Uri.parse(_apiUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: json.encode({
          'model': 'moonshot-v1-8k',
          'messages': messages,
          'temperature': 0.3,
        }),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final aiReply = data['choices'][0]['message']['content'] as String;
        return aiReply.trim();
      } else {
        debugPrint('API Error: ${response.statusCode} - ${response.body}');
        throw Exception('AI服务暂时不可用，请稍后重试');
      }
    } catch (e) {
      debugPrint('发送消息失败: $e');
      throw Exception('网络连接失败，请检查网络设置');
    }
  }
  
  /// 构建AI角色的系统提示
  String _buildSystemPrompt(CrrAiMaster83 master) {
    return '''你是${master.name}，一个专业的${master.category}废品利用创意指导师。
    
你的专长包括：${master.specialties.join('、')}

你的角色描述：${master.longDescription}

请以友好、专业的语气回答用户关于废品利用和创意改造的问题。回答要具体、实用，并鼓励用户发挥创意。回答长度控制在200字以内。''';
  }
  
  /// 保存聊天会话到本地
  Future<void> saveConversationSession(CrrChatSession56 session) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessions = await getAllConversationSessions();

      // 查找是否已存在该会话或同一角色的会话
      final existingIndex = sessions.indexWhere((s) => s.sessionId == session.sessionId);
      final sameAiIndex = sessions.indexWhere((s) => s.masterId == session.masterId && s.sessionId != session.sessionId);

      if (existingIndex >= 0) {
        // 更新现有会话
        sessions[existingIndex] = session;
      } else if (sameAiIndex >= 0) {
        // 合并到同一AI角色的现有会话中
        final existingSession = sessions[sameAiIndex];
        final mergedMessages = List<CrrChatMessage91>.from(existingSession.messages);
        mergedMessages.addAll(session.messages);

        final mergedSession = existingSession.copyWith(
          messages: mergedMessages,
          lastMessageAt: session.lastMessageAt,
        );

        sessions[sameAiIndex] = mergedSession;
      } else {
        // 添加新会话
        sessions.add(session);
      }

      // 按最后消息时间排序
      sessions.sort((a, b) => b.lastMessageAt.compareTo(a.lastMessageAt));

      // 保存到本地存储
      final sessionsJson = sessions.map((s) => s.toJson()).toList();
      await prefs.setString(_storageKey, json.encode(sessionsJson));

      debugPrint('会话已保存: ${session.sessionId}');
    } catch (e) {
      debugPrint('保存会话失败: $e');
    }
  }
  
  /// 获取所有聊天会话
  Future<List<CrrChatSession56>> getAllConversationSessions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionsJson = prefs.getString(_storageKey);
      
      if (sessionsJson == null) return [];
      
      final sessionsList = json.decode(sessionsJson) as List;
      return sessionsList
          .map((json) => CrrChatSession56.fromJson(json))
          .toList();
    } catch (e) {
      debugPrint('获取会话列表失败: $e');
      return [];
    }
  }
  
  /// 获取特定AI角色的聊天会话
  Future<List<CrrChatSession56>> getSessionsByMaster(String masterId) async {
    final allSessions = await getAllConversationSessions();
    return allSessions.where((session) => session.masterId == masterId).toList();
  }
  
  /// 获取特定会话的详细信息
  Future<CrrChatSession56?> getSessionById(String sessionId) async {
    final allSessions = await getAllConversationSessions();
    try {
      return allSessions.firstWhere((session) => session.sessionId == sessionId);
    } catch (e) {
      return null;
    }
  }
  
  /// 删除聊天会话
  Future<void> deleteConversationSession(String sessionId) async {
    try {
      final sessions = await getAllConversationSessions();
      sessions.removeWhere((session) => session.sessionId == sessionId);
      
      final prefs = await SharedPreferences.getInstance();
      final sessionsJson = sessions.map((s) => s.toJson()).toList();
      await prefs.setString(_storageKey, json.encode(sessionsJson));
      
      debugPrint('会话已删除: $sessionId');
    } catch (e) {
      debugPrint('删除会话失败: $e');
    }
  }
  
  /// 清空所有聊天记录
  Future<void> clearAllConversations() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_storageKey);
      debugPrint('所有会话已清空');
    } catch (e) {
      debugPrint('清空会话失败: $e');
    }
  }
  
  /// 获取聊天统计信息
  Future<CrrChatStats38> getConversationStats() async {
    final sessions = await getAllConversationSessions();
    
    int totalMessages = 0;
    final masterChatCounts = <String, int>{};
    DateTime? lastChatTime;
    
    for (final session in sessions) {
      totalMessages += session.messageCount;
      masterChatCounts[session.masterId] = 
          (masterChatCounts[session.masterId] ?? 0) + session.messageCount;
      
      if (lastChatTime == null || session.lastMessageAt.isAfter(lastChatTime)) {
        lastChatTime = session.lastMessageAt;
      }
    }
    
    return CrrChatStats38(
      totalSessions: sessions.length,
      totalMessages: totalMessages,
      activeSessions: sessions.where((s) => s.isActive).length,
      masterChatCounts: masterChatCounts,
      lastChatTime: lastChatTime,
    );
  }
}
