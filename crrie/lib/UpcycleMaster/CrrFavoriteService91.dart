import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 收藏功能服务类
/// 负责管理AI角色的收藏状态和本地持久化存储
class CrrFavoriteService91 {
  static const String _storageKey = 'crr_favorite_masters';
  
  /// 获取所有收藏的AI角色ID列表
  Future<List<String>> getFavoriteMasterIds() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getString(_storageKey);
      
      if (favoritesJson == null) return [];
      
      final favoritesList = json.decode(favoritesJson) as List;
      return favoritesList.cast<String>();
    } catch (e) {
      debugPrint('获取收藏列表失败: $e');
      return [];
    }
  }
  
  /// 保存收藏的AI角色ID列表
  Future<void> saveFavoriteMasterIds(List<String> masterIds) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = json.encode(masterIds);
      await prefs.setString(_storageKey, favoritesJson);
      
      debugPrint('收藏列表已保存: ${masterIds.length} 个角色');
    } catch (e) {
      debugPrint('保存收藏列表失败: $e');
    }
  }
  
  /// 添加AI角色到收藏
  Future<bool> addToFavorites(String masterId) async {
    try {
      final currentFavorites = await getFavoriteMasterIds();
      
      if (!currentFavorites.contains(masterId)) {
        currentFavorites.add(masterId);
        await saveFavoriteMasterIds(currentFavorites);
        debugPrint('已添加到收藏: $masterId');
        return true;
      }
      
      return false; // 已经在收藏中
    } catch (e) {
      debugPrint('添加收藏失败: $e');
      return false;
    }
  }
  
  /// 从收藏中移除AI角色
  Future<bool> removeFromFavorites(String masterId) async {
    try {
      final currentFavorites = await getFavoriteMasterIds();
      
      if (currentFavorites.contains(masterId)) {
        currentFavorites.remove(masterId);
        await saveFavoriteMasterIds(currentFavorites);
        debugPrint('已从收藏中移除: $masterId');
        return true;
      }
      
      return false; // 不在收藏中
    } catch (e) {
      debugPrint('移除收藏失败: $e');
      return false;
    }
  }
  
  /// 切换AI角色的收藏状态
  Future<bool> toggleFavorite(String masterId) async {
    try {
      final currentFavorites = await getFavoriteMasterIds();
      
      if (currentFavorites.contains(masterId)) {
        // 当前已收藏，移除收藏
        await removeFromFavorites(masterId);
        return false; // 返回false表示已取消收藏
      } else {
        // 当前未收藏，添加收藏
        await addToFavorites(masterId);
        return true; // 返回true表示已添加收藏
      }
    } catch (e) {
      debugPrint('切换收藏状态失败: $e');
      return false;
    }
  }
  
  /// 检查AI角色是否已收藏
  Future<bool> isFavorited(String masterId) async {
    try {
      final currentFavorites = await getFavoriteMasterIds();
      return currentFavorites.contains(masterId);
    } catch (e) {
      debugPrint('检查收藏状态失败: $e');
      return false;
    }
  }
  
  /// 获取收藏数量
  Future<int> getFavoriteCount() async {
    try {
      final currentFavorites = await getFavoriteMasterIds();
      return currentFavorites.length;
    } catch (e) {
      debugPrint('获取收藏数量失败: $e');
      return 0;
    }
  }
  
  /// 清空所有收藏
  Future<void> clearAllFavorites() async {
    try {
      await saveFavoriteMasterIds([]);
      debugPrint('已清空所有收藏');
    } catch (e) {
      debugPrint('清空收藏失败: $e');
    }
  }
  
  /// 批量添加收藏
  Future<void> addMultipleToFavorites(List<String> masterIds) async {
    try {
      final currentFavorites = await getFavoriteMasterIds();
      
      for (final masterId in masterIds) {
        if (!currentFavorites.contains(masterId)) {
          currentFavorites.add(masterId);
        }
      }
      
      await saveFavoriteMasterIds(currentFavorites);
      debugPrint('批量添加收藏完成: ${masterIds.length} 个角色');
    } catch (e) {
      debugPrint('批量添加收藏失败: $e');
    }
  }
  
  /// 批量移除收藏
  Future<void> removeMultipleFromFavorites(List<String> masterIds) async {
    try {
      final currentFavorites = await getFavoriteMasterIds();
      
      for (final masterId in masterIds) {
        currentFavorites.remove(masterId);
      }
      
      await saveFavoriteMasterIds(currentFavorites);
      debugPrint('批量移除收藏完成: ${masterIds.length} 个角色');
    } catch (e) {
      debugPrint('批量移除收藏失败: $e');
    }
  }
  
  /// 获取收藏统计信息
  Future<Map<String, int>> getFavoriteStats() async {
    try {
      final currentFavorites = await getFavoriteMasterIds();
      
      // 这里可以根据需要添加更多统计信息
      // 比如按分类统计收藏数量等
      
      return {
        'total': currentFavorites.length,
        'recent': currentFavorites.length, // 可以根据时间戳计算最近收藏的数量
      };
    } catch (e) {
      debugPrint('获取收藏统计失败: $e');
      return {'total': 0, 'recent': 0};
    }
  }
  
  /// 导出收藏列表
  Future<String> exportFavorites() async {
    try {
      final currentFavorites = await getFavoriteMasterIds();
      return json.encode({
        'favorites': currentFavorites,
        'exportTime': DateTime.now().toIso8601String(),
        'version': '1.0',
      });
    } catch (e) {
      debugPrint('导出收藏列表失败: $e');
      return '{}';
    }
  }
  
  /// 导入收藏列表
  Future<bool> importFavorites(String jsonData) async {
    try {
      final data = json.decode(jsonData) as Map<String, dynamic>;
      final favorites = (data['favorites'] as List).cast<String>();
      
      await saveFavoriteMasterIds(favorites);
      debugPrint('导入收藏列表成功: ${favorites.length} 个角色');
      return true;
    } catch (e) {
      debugPrint('导入收藏列表失败: $e');
      return false;
    }
  }
}
