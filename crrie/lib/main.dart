import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'ReclaimFlow/CrrUserProvider52.dart';
import 'ReclaimFlow/CrrAiMasterProvider76.dart';
import 'CraftAtelier/CrrAiMaster67Hub.dart';
import 'CraftAtelier/CrrCoinManager78.dart';
import 'RemakeParts/CrrNeumorphic93.dart';
import 'RemakeParts/CrrFloatingNav42.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化金币管理器
  await CrrCoinManager78.instance.initialize();

  runApp(const CrrieApp());
}

/// Crrie应用主入口
/// 家庭废品利用创意指导师应用
class CrrieApp extends StatelessWidget {
  const CrrieApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => CrrUserProvider52()),
        ChangeNotifierProvider(create: (_) => CrrAiMasterProvider76()),
        ChangeNotifierProvider(create: (_) => CrrCoinManager78.instance),
      ],
      child: MaterialApp(
        title: 'Crrie - AI Household Waste Upcycling Guides',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          // 新拟物化主题配置
          primarySwatch: Colors.green,
          primaryColor: CrrNeumorphic93.primaryColor,
          scaffoldBackgroundColor: CrrNeumorphic93.backgroundColor,
          fontFamily: 'SF Pro Display', // iOS风格字体

          // 文本主题
          textTheme: TextTheme(
            headlineLarge: TextStyle(
              color: CrrNeumorphic93.textPrimary,
              fontWeight: FontWeight.bold,
            ),
            headlineMedium: TextStyle(
              color: CrrNeumorphic93.textPrimary,
              fontWeight: FontWeight.w600,
            ),
            bodyLarge: TextStyle(
              color: CrrNeumorphic93.textPrimary,
            ),
            bodyMedium: TextStyle(
              color: CrrNeumorphic93.textSecondary,
            ),
          ),

          // AppBar主题
          appBarTheme: AppBarTheme(
            backgroundColor: CrrNeumorphic93.backgroundColor,
            elevation: 0,
            titleTextStyle: TextStyle(
              color: CrrNeumorphic93.textPrimary,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
            iconTheme: IconThemeData(
              color: CrrNeumorphic93.textPrimary,
            ),
          ),

          // 按钮主题
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              backgroundColor: CrrNeumorphic93.primaryColor,
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
          ),

          // 输入框主题
          inputDecorationTheme: InputDecorationTheme(
            filled: true,
            fillColor: CrrNeumorphic93.cardColor,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide.none,
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),

          // 卡片主题
          cardTheme: CardTheme(
            color: CrrNeumorphic93.cardColor,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
          ),

          useMaterial3: true,
        ),
        home: const CrrMainContainer(),
      ),
    );
  }
}
