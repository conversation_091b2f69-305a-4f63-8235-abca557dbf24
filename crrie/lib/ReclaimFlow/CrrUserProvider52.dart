import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../ScrapVault/CrrUserModel47.dart';
import '../ScrapVault/CrrSoulQuest72.dart';

/// 用户状态管理Provider
/// 管理用户信息、金币、偏好设置等状态
class CrrUserProvider52 extends ChangeNotifier {
  CrrUserModel47? _currentUser;
  bool _isLoading = false;
  String? _error;
  
  // Getters
  CrrUserModel47? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLoggedIn => _currentUser != null;

  /// 初始化用户数据
  Future<void> initializeUser() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString('current_user');
      
      if (userJson != null) {
        final userData = json.decode(userJson);
        _currentUser = CrrUserModel47.fromJson(userData);
        
        // 更新最后活跃时间
        await updateLastActiveTime();
      } else {
        // 创建新用户
        await createNewUser();
      }
      
      _clearError();
    } catch (e) {
      _setError('Failed to initialize user: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 创建新用户
  Future<void> createNewUser({
    String? nickname,
    String? avatarPath,
  }) async {
    try {
      _currentUser = CrrUserModel47.createNew(
        nickname: nickname,
        avatarPath: avatarPath,
      );
      
      await _saveUserToStorage();
      notifyListeners();
    } catch (e) {
      _setError('Failed to create new user: $e');
    }
  }

  /// 更新用户昵称
  Future<void> updateNickname(String newNickname) async {
    if (_currentUser == null) return;
    
    try {
      _currentUser = _currentUser!.copyWith(nickname: newNickname);
      await _saveUserToStorage();
      notifyListeners();
    } catch (e) {
      _setError('Failed to update nickname: $e');
    }
  }

  /// 更新用户头像
  Future<void> updateAvatar(String newAvatarPath) async {
    if (_currentUser == null) return;
    
    try {
      _currentUser = _currentUser!.copyWith(avatarPath: newAvatarPath);
      await _saveUserToStorage();
      notifyListeners();
    } catch (e) {
      _setError('Failed to update avatar: $e');
    }
  }

  /// 添加金币
  Future<void> addCoins(int amount) async {
    if (_currentUser == null || amount <= 0) return;
    
    try {
      final newTotal = _currentUser!.totalCoins + amount;
      _currentUser = _currentUser!.copyWith(totalCoins: newTotal);
      await _saveUserToStorage();
      notifyListeners();
    } catch (e) {
      _setError('Failed to add coins: $e');
    }
  }

  /// 消费金币
  Future<bool> spendCoins(int amount) async {
    if (_currentUser == null || amount <= 0) return false;
    
    if (_currentUser!.totalCoins < amount) {
      _setError('Insufficient coins');
      return false;
    }
    
    try {
      final newTotal = _currentUser!.totalCoins - amount;
      _currentUser = _currentUser!.copyWith(totalCoins: newTotal);
      await _saveUserToStorage();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to spend coins: $e');
      return false;
    }
  }

  /// 回答灵魂问题并获得奖励
  Future<bool> answerSoulQuestion(CrrSoulQuest72 question, String answer) async {
    if (_currentUser == null) return false;
    
    // 检查今天是否已经回答过
    if (!_currentUser!.canAnswerSoulQuestionToday) {
      _setError('You have already answered today\'s soul question');
      return false;
    }
    
    try {
      // 更新最后回答时间和金币
      _currentUser = _currentUser!.copyWith(
        lastSoulQuestionDate: DateTime.now(),
        totalCoins: _currentUser!.totalCoins + question.rewardCoins,
      );
      
      await _saveUserToStorage();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to process soul question answer: $e');
      return false;
    }
  }

  /// 增加与AI角色的聊天次数
  Future<void> incrementChatCount(String masterId) async {
    if (_currentUser == null) return;
    
    try {
      final updatedCounts = Map<String, int>.from(_currentUser!.masterChatCounts);
      updatedCounts[masterId] = (updatedCounts[masterId] ?? 0) + 1;
      
      _currentUser = _currentUser!.copyWith(masterChatCounts: updatedCounts);
      await _saveUserToStorage();
      notifyListeners();
    } catch (e) {
      _setError('Failed to increment chat count: $e');
    }
  }

  /// 添加收藏的AI角色
  Future<void> addFavoriteMaster(String masterId) async {
    if (_currentUser == null) return;
    
    try {
      final updatedFavorites = List<String>.from(_currentUser!.favoriteMasterIds);
      if (!updatedFavorites.contains(masterId)) {
        updatedFavorites.add(masterId);
        _currentUser = _currentUser!.copyWith(favoriteMasterIds: updatedFavorites);
        await _saveUserToStorage();
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to add favorite master: $e');
    }
  }

  /// 移除收藏的AI角色
  Future<void> removeFavoriteMaster(String masterId) async {
    if (_currentUser == null) return;

    try {
      final updatedFavorites = List<String>.from(_currentUser!.favoriteMasterIds);
      updatedFavorites.remove(masterId);
      _currentUser = _currentUser!.copyWith(favoriteMasterIds: updatedFavorites);
      await _saveUserToStorage();
      notifyListeners();
    } catch (e) {
      _setError('Failed to remove favorite master: $e');
    }
  }

  /// 切换收藏状态
  Future<void> toggleMasterFavorite(String masterId) async {
    if (_currentUser == null) return;

    final isFavorited = _currentUser!.isMasterFavorited(masterId);
    if (isFavorited) {
      await removeFavoriteMaster(masterId);
    } else {
      await addFavoriteMaster(masterId);
    }
  }

  /// 更新用户偏好设置
  Future<void> updatePreference(String key, dynamic value) async {
    if (_currentUser == null) return;
    
    try {
      final updatedPreferences = Map<String, dynamic>.from(_currentUser!.preferences);
      updatedPreferences[key] = value;
      _currentUser = _currentUser!.copyWith(preferences: updatedPreferences);
      await _saveUserToStorage();
      notifyListeners();
    } catch (e) {
      _setError('Failed to update preference: $e');
    }
  }

  /// 标记首次启动完成
  Future<void> markFirstLaunchComplete() async {
    if (_currentUser == null) return;
    
    try {
      _currentUser = _currentUser!.copyWith(isFirstLaunch: false);
      await _saveUserToStorage();
      notifyListeners();
    } catch (e) {
      _setError('Failed to mark first launch complete: $e');
    }
  }

  /// 更新最后活跃时间
  Future<void> updateLastActiveTime() async {
    if (_currentUser == null) return;
    
    try {
      _currentUser = _currentUser!.copyWith(lastActiveAt: DateTime.now());
      await _saveUserToStorage();
      // 不需要notifyListeners，因为这是后台更新
    } catch (e) {
      // 静默失败，不影响用户体验
      debugPrint('Failed to update last active time: $e');
    }
  }

  /// 重置用户数据
  Future<void> resetUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user');
      _currentUser = null;
      notifyListeners();
    } catch (e) {
      _setError('Failed to reset user data: $e');
    }
  }

  /// 保存用户数据到本地存储
  Future<void> _saveUserToStorage() async {
    if (_currentUser == null) return;
    
    final prefs = await SharedPreferences.getInstance();
    final userJson = json.encode(_currentUser!.toJson());
    await prefs.setString('current_user', userJson);
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置错误信息
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// 清除错误信息
  void _clearError() {
    _error = null;
    notifyListeners();
  }

  /// 获取用户统计信息
  CrrUserStats64 getUserStats() {
    if (_currentUser == null) {
      return const CrrUserStats64(
        totalChats: 0,
        coinsEarned: 0,
        soulQuestionsAnswered: 0,
        favoriteMasters: 0,
        activeDays: 0,
        categoryChats: {},
      );
    }
    
    return CrrUserStats64.fromUser(_currentUser!);
  }
}
