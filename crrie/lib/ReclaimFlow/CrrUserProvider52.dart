import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../ScrapVault/CrrUserModel47.dart';
import '../ScrapVault/CrrSoulQuest72.dart';
import '../UpcycleMaster/CrrFavoriteService91.dart';
import '../CraftAtelier/CrrCoinManager78.dart';

/// 用户状态管理Provider
/// 管理用户信息、金币、偏好设置等状态
class CrrUserProvider52 extends ChangeNotifier {
  CrrUserModel47? _currentUser;
  bool _isLoading = false;
  String? _error;
  final CrrFavoriteService91 _favoriteService = CrrFavoriteService91();
  
  // Getters
  CrrUserModel47? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLoggedIn => _currentUser != null;

  /// 初始化用户数据
  Future<void> initializeUser() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString('current_user');
      
      if (userJson != null) {
        final userData = json.decode(userJson);
        _currentUser = CrrUserModel47.fromJson(userData);
        
        // 更新最后活跃时间
        await updateLastActiveTime();
      } else {
        // 创建新用户
        await createNewUser();
      }
      
      _clearError();
    } catch (e) {
      _setError('Failed to initialize user: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 创建新用户
  Future<void> createNewUser({
    String? nickname,
    String? avatarPath,
  }) async {
    try {
      _currentUser = CrrUserModel47.createNew(
        nickname: nickname,
        avatarPath: avatarPath,
      );
      
      await _saveUserToStorage();
      notifyListeners();
    } catch (e) {
      _setError('Failed to create new user: $e');
    }
  }

  /// 更新用户昵称
  Future<void> updateNickname(String newNickname) async {
    if (_currentUser == null) return;
    
    try {
      _currentUser = _currentUser!.copyWith(nickname: newNickname);
      await _saveUserToStorage();
      notifyListeners();
    } catch (e) {
      _setError('Failed to update nickname: $e');
    }
  }

  /// 更新用户头像
  Future<void> updateAvatar(String newAvatarPath) async {
    if (_currentUser == null) return;
    
    try {
      _currentUser = _currentUser!.copyWith(avatarPath: newAvatarPath);
      await _saveUserToStorage();
      notifyListeners();
    } catch (e) {
      _setError('Failed to update avatar: $e');
    }
  }



  /// 回答每日启发问题并获得奖励
  Future<bool> answerSoulQuestion(CrrSoulQuest72 question, String answer) async {
    if (_currentUser == null) return false;

    // 检查今天是否已经回答过
    if (!_currentUser!.canAnswerSoulQuestionToday) {
      _setError('You have already answered today\'s challenge');
      return false;
    }

    try {
      // 保存回答历史
      await _saveSoulAnswer(question, answer);

      // 更新最后回答时间和回答次数
      _currentUser = _currentUser!.copyWith(
        lastSoulQuestionDate: DateTime.now(),
        answeredSoulQuestions: _currentUser!.answeredSoulQuestions + 1,
      );

      // 通过CoinManager添加宝石奖励
      final coinManager = CrrCoinManager78.instance;
      await coinManager.addCoins(question.rewardCoins);

      await _saveUserToStorage();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to process challenge answer: $e');
      return false;
    }
  }

  /// 保存回答历史
  Future<void> _saveSoulAnswer(CrrSoulQuest72 question, String answer) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final answersJson = prefs.getString('soul_answers') ?? '[]';
      final List<dynamic> answers = json.decode(answersJson);

      final answerData = CrrSoulAnswer84(
        answerId: DateTime.now().millisecondsSinceEpoch.toString(),
        questionId: question.questionId,
        userId: _currentUser!.userId,
        answerText: answer,
        answeredAt: DateTime.now(),
        coinsEarned: question.rewardCoins,
        isRewarded: true,
      );

      answers.add(answerData.toJson());
      await prefs.setString('soul_answers', json.encode(answers));
    } catch (e) {
      print('Failed to save soul answer: $e');
    }
  }

  /// 获取回答历史
  Future<List<CrrSoulAnswer84>> getSoulAnswers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final answersJson = prefs.getString('soul_answers') ?? '[]';
      final List<dynamic> answers = json.decode(answersJson);

      return answers.map((json) => CrrSoulAnswer84.fromJson(json)).toList()
        ..sort((a, b) => b.answeredAt.compareTo(a.answeredAt));
    } catch (e) {
      print('Failed to load soul answers: $e');
      return [];
    }
  }

  /// 增加与AI角色的聊天次数
  Future<void> incrementChatCount(String masterId) async {
    if (_currentUser == null) return;
    
    try {
      final updatedCounts = Map<String, int>.from(_currentUser!.masterChatCounts);
      updatedCounts[masterId] = (updatedCounts[masterId] ?? 0) + 1;
      
      _currentUser = _currentUser!.copyWith(masterChatCounts: updatedCounts);
      await _saveUserToStorage();
      notifyListeners();
    } catch (e) {
      _setError('Failed to increment chat count: $e');
    }
  }

  /// 添加收藏的AI角色
  Future<void> addFavoriteMaster(String masterId) async {
    if (_currentUser == null) return;
    
    try {
      final updatedFavorites = List<String>.from(_currentUser!.favoriteMasterIds);
      if (!updatedFavorites.contains(masterId)) {
        updatedFavorites.add(masterId);
        _currentUser = _currentUser!.copyWith(favoriteMasterIds: updatedFavorites);
        await _saveUserToStorage();
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to add favorite master: $e');
    }
  }

  /// 移除收藏的AI角色
  Future<void> removeFavoriteMaster(String masterId) async {
    if (_currentUser == null) return;

    try {
      final updatedFavorites = List<String>.from(_currentUser!.favoriteMasterIds);
      updatedFavorites.remove(masterId);
      _currentUser = _currentUser!.copyWith(favoriteMasterIds: updatedFavorites);
      await _saveUserToStorage();
      notifyListeners();
    } catch (e) {
      _setError('Failed to remove favorite master: $e');
    }
  }

  /// 切换收藏状态
  Future<void> toggleMasterFavorite(String masterId) async {
    if (_currentUser == null) return;

    final isFavorited = _currentUser!.isMasterFavorited(masterId);
    if (isFavorited) {
      await removeFavoriteMaster(masterId);
    } else {
      await addFavoriteMaster(masterId);
    }
  }

  /// 更新用户偏好设置
  Future<void> updatePreference(String key, dynamic value) async {
    if (_currentUser == null) return;
    
    try {
      final updatedPreferences = Map<String, dynamic>.from(_currentUser!.preferences);
      updatedPreferences[key] = value;
      _currentUser = _currentUser!.copyWith(preferences: updatedPreferences);
      await _saveUserToStorage();
      notifyListeners();
    } catch (e) {
      _setError('Failed to update preference: $e');
    }
  }

  /// 标记首次启动完成
  Future<void> markFirstLaunchComplete() async {
    if (_currentUser == null) return;
    
    try {
      _currentUser = _currentUser!.copyWith(isFirstLaunch: false);
      await _saveUserToStorage();
      notifyListeners();
    } catch (e) {
      _setError('Failed to mark first launch complete: $e');
    }
  }

  /// 更新最后活跃时间
  Future<void> updateLastActiveTime() async {
    if (_currentUser == null) return;
    
    try {
      _currentUser = _currentUser!.copyWith(lastActiveAt: DateTime.now());
      await _saveUserToStorage();
      // 不需要notifyListeners，因为这是后台更新
    } catch (e) {
      // 静默失败，不影响用户体验
      debugPrint('Failed to update last active time: $e');
    }
  }

  /// 重置用户数据
  Future<void> resetUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user');
      _currentUser = null;
      notifyListeners();
    } catch (e) {
      _setError('Failed to reset user data: $e');
    }
  }

  /// 保存用户数据到本地存储
  Future<void> _saveUserToStorage() async {
    if (_currentUser == null) return;
    
    final prefs = await SharedPreferences.getInstance();
    final userJson = json.encode(_currentUser!.toJson());
    await prefs.setString('current_user', userJson);
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置错误信息
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// 清除错误信息
  void _clearError() {
    _error = null;
    notifyListeners();
  }

  /// 获取用户统计信息
  CrrUserStats64 getUserStats() {
    if (_currentUser == null) {
      return const CrrUserStats64(
        totalChats: 0,
        coinsEarned: 0,
        soulQuestionsAnswered: 0,
        favoriteMasters: 0,
        activeDays: 0,
        categoryChats: {},
      );
    }

    // 从CoinManager获取当前宝石余额
    final coinManager = CrrCoinManager78.instance;
    return CrrUserStats64(
      totalChats: _currentUser!.totalChatCount,
      coinsEarned: coinManager.coinBalance, // 使用CoinManager的余额
      soulQuestionsAnswered: _currentUser!.answeredSoulQuestions,
      favoriteMasters: _currentUser!.favoriteCount,
      activeDays: _currentUser!.activeDays,
      categoryChats: {}, // 需要从聊天记录计算
    );
  }

  // ==================== 收藏功能管理 ====================

  /// 添加AI角色到收藏
  Future<void> addMasterToFavorites(String masterId) async {
    if (_currentUser == null) return;

    try {
      final currentFavorites = List<String>.from(_currentUser!.favoriteMasterIds);

      if (!currentFavorites.contains(masterId)) {
        currentFavorites.add(masterId);
        await _favoriteService.addToFavorites(masterId);

        _currentUser = _currentUser!.copyWith(
          favoriteMasterIds: currentFavorites,
        );

        await _saveUserToStorage();
        notifyListeners();

        debugPrint('已添加到收藏: $masterId');
      }
    } catch (e) {
      debugPrint('添加收藏失败: $e');
      _setError('Failed to add to favorites');
    }
  }

  /// 从收藏中移除AI角色
  Future<void> removeMasterFromFavorites(String masterId) async {
    if (_currentUser == null) return;

    try {
      final currentFavorites = List<String>.from(_currentUser!.favoriteMasterIds);

      if (currentFavorites.contains(masterId)) {
        currentFavorites.remove(masterId);
        await _favoriteService.removeFromFavorites(masterId);

        _currentUser = _currentUser!.copyWith(
          favoriteMasterIds: currentFavorites,
        );

        await _saveUserToStorage();
        notifyListeners();

        debugPrint('已从收藏中移除: $masterId');
      }
    } catch (e) {
      debugPrint('移除收藏失败: $e');
      _setError('Failed to remove from favorites');
    }
  }

  /// 批量添加收藏
  Future<void> addMultipleToFavorites(List<String> masterIds) async {
    if (_currentUser == null) return;

    try {
      final currentFavorites = List<String>.from(_currentUser!.favoriteMasterIds);
      bool hasChanges = false;

      for (final masterId in masterIds) {
        if (!currentFavorites.contains(masterId)) {
          currentFavorites.add(masterId);
          hasChanges = true;
        }
      }

      if (hasChanges) {
        await _favoriteService.addMultipleToFavorites(masterIds);

        _currentUser = _currentUser!.copyWith(
          favoriteMasterIds: currentFavorites,
        );

        await _saveUserToStorage();
        notifyListeners();

        debugPrint('批量添加收藏完成: ${masterIds.length} 个角色');
      }
    } catch (e) {
      debugPrint('批量添加收藏失败: $e');
      _setError('Failed to add multiple favorites');
    }
  }

  /// 清空所有收藏
  Future<void> clearAllFavorites() async {
    if (_currentUser == null) return;

    try {
      await _favoriteService.clearAllFavorites();

      _currentUser = _currentUser!.copyWith(
        favoriteMasterIds: [],
      );

      await _saveUserToStorage();
      notifyListeners();

      debugPrint('已清空所有收藏');
    } catch (e) {
      debugPrint('清空收藏失败: $e');
      _setError('Failed to clear favorites');
    }
  }

  /// 同步收藏数据（从本地存储加载）
  Future<void> syncFavoriteData() async {
    if (_currentUser == null) return;

    try {
      final favoriteIds = await _favoriteService.getFavoriteMasterIds();

      _currentUser = _currentUser!.copyWith(
        favoriteMasterIds: favoriteIds,
      );

      await _saveUserToStorage();
      notifyListeners();

      debugPrint('收藏数据同步完成: ${favoriteIds.length} 个角色');
    } catch (e) {
      debugPrint('同步收藏数据失败: $e');
    }
  }
}
