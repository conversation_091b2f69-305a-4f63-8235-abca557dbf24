import 'package:flutter/foundation.dart';
import '../ScrapVault/CrrAiMaster83.dart';

/// AI角色状态管理Provider
/// 管理AI角色数据、分类筛选、搜索等功能
class CrrAiMasterProvider76 extends ChangeNotifier {
  List<CrrAiMaster83> _allMasters = [];
  List<CrrAiMaster83> _filteredMasters = [];
  String _selectedCategory = 'All';
  String _searchQuery = '';
  bool _isLoading = false;
  String? _error;
  
  // Getters
  List<CrrAiMaster83> get allMasters => _allMasters;
  List<CrrAiMaster83> get filteredMasters => _filteredMasters;
  String get selectedCategory => _selectedCategory;
  String get searchQuery => _searchQuery;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// 初始化AI角色数据
  Future<void> initializeMasters() async {
    _setLoading(true);
    try {
      _allMasters = CrrAiMasterRepo92.getAllMasters();
      _filteredMasters = List.from(_allMasters);
      _clearError();
    } catch (e) {
      _setError('Failed to load AI masters: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 根据分类筛选AI角色
  void filterByCategory(String category) {
    _selectedCategory = category;
    _applyFilters();
    notifyListeners();
  }

  /// 搜索AI角色
  void searchMasters(String query) {
    _searchQuery = query;
    _applyFilters();
    notifyListeners();
  }

  /// 清除搜索
  void clearSearch() {
    _searchQuery = '';
    _applyFilters();
    notifyListeners();
  }

  /// 重置所有筛选
  void resetFilters() {
    _selectedCategory = 'All';
    _searchQuery = '';
    _applyFilters();
    notifyListeners();
  }

  /// 应用筛选条件
  void _applyFilters() {
    _filteredMasters = _allMasters.where((master) {
      // 分类筛选
      bool categoryMatch = _selectedCategory == 'All' || 
                          master.category == _selectedCategory;
      
      // 搜索筛选
      bool searchMatch = _searchQuery.isEmpty ||
                        master.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                        master.shortDescription.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                        master.specialties.any((specialty) => 
                          specialty.toLowerCase().contains(_searchQuery.toLowerCase()));
      
      return categoryMatch && searchMatch;
    }).toList();
  }

  /// 获取所有分类
  List<String> getAllCategories() {
    final categories = ['All'];
    categories.addAll(CrrAiMasterRepo92.getAllCategories());
    return categories;
  }

  /// 根据ID获取AI角色
  CrrAiMaster83? getMasterById(String masterId) {
    try {
      return _allMasters.firstWhere((master) => master.masterId == masterId);
    } catch (e) {
      return null;
    }
  }

  /// 获取推荐的AI角色（基于用户偏好）
  List<CrrAiMaster83> getRecommendedMasters({
    List<String>? favoriteMasterIds,
    Map<String, int>? chatCounts,
    int limit = 3,
  }) {
    // 简单的推荐算法：
    // 1. 优先推荐收藏的角色
    // 2. 推荐聊天次数较少的角色
    // 3. 推荐不同分类的角色
    
    final recommended = <CrrAiMaster83>[];
    final usedCategories = <String>{};
    
    // 添加收藏的角色
    if (favoriteMasterIds != null) {
      for (final masterId in favoriteMasterIds) {
        final master = getMasterById(masterId);
        if (master != null && recommended.length < limit) {
          recommended.add(master);
          usedCategories.add(master.category);
        }
      }
    }
    
    // 添加其他角色（优先选择不同分类的）
    for (final master in _allMasters) {
      if (recommended.length >= limit) break;
      
      if (!recommended.contains(master)) {
        // 优先选择新分类的角色
        if (!usedCategories.contains(master.category)) {
          recommended.add(master);
          usedCategories.add(master.category);
        }
      }
    }
    
    // 如果还没达到限制数量，添加剩余角色
    for (final master in _allMasters) {
      if (recommended.length >= limit) break;
      
      if (!recommended.contains(master)) {
        recommended.add(master);
      }
    }
    
    return recommended;
  }

  /// 获取热门AI角色（基于聊天次数）
  List<CrrAiMaster83> getPopularMasters({
    Map<String, int>? globalChatCounts,
    int limit = 5,
  }) {
    if (globalChatCounts == null || globalChatCounts.isEmpty) {
      // 如果没有统计数据，返回前几个角色
      return _allMasters.take(limit).toList();
    }
    
    // 根据聊天次数排序
    final mastersWithCounts = _allMasters.map((master) {
      final chatCount = globalChatCounts[master.masterId] ?? 0;
      return MapEntry(master, chatCount);
    }).toList();
    
    mastersWithCounts.sort((a, b) => b.value.compareTo(a.value));
    
    return mastersWithCounts
        .take(limit)
        .map((entry) => entry.key)
        .toList();
  }

  /// 获取新手推荐AI角色
  List<CrrAiMaster83> getBeginnerFriendlyMasters({int limit = 3}) {
    // 选择一些容易上手的角色
    final beginnerFriendlyIds = [
      'master_001', // 纸箱改造
      'master_002', // 塑料瓶利用
      'master_005', // 报纸杂志利用
    ];
    
    final beginnerMasters = <CrrAiMaster83>[];
    
    for (final masterId in beginnerFriendlyIds) {
      final master = getMasterById(masterId);
      if (master != null && beginnerMasters.length < limit) {
        beginnerMasters.add(master);
      }
    }
    
    return beginnerMasters;
  }

  /// 根据专业领域搜索
  List<CrrAiMaster83> searchBySpecialty(String specialty) {
    return _allMasters.where((master) {
      return master.specialties.any((s) => 
        s.toLowerCase().contains(specialty.toLowerCase()));
    }).toList();
  }

  /// 获取分类统计
  Map<String, int> getCategoryStats() {
    final stats = <String, int>{};
    
    for (final master in _allMasters) {
      stats[master.category] = (stats[master.category] ?? 0) + 1;
    }
    
    return stats;
  }

  /// 获取随机AI角色
  CrrAiMaster83? getRandomMaster({List<String>? excludeIds}) {
    var availableMasters = _allMasters;
    
    if (excludeIds != null && excludeIds.isNotEmpty) {
      availableMasters = _allMasters.where((master) => 
        !excludeIds.contains(master.masterId)).toList();
    }
    
    if (availableMasters.isEmpty) return null;
    
    final randomIndex = DateTime.now().millisecondsSinceEpoch % availableMasters.length;
    return availableMasters[randomIndex];
  }

  /// 检查AI角色是否可用
  bool isMasterAvailable(String masterId) {
    final master = getMasterById(masterId);
    return master != null;
  }

  /// 获取相似的AI角色
  List<CrrAiMaster83> getSimilarMasters(String masterId, {int limit = 3}) {
    final targetMaster = getMasterById(masterId);
    if (targetMaster == null) return [];
    
    // 基于分类和专业领域找相似角色
    final similar = _allMasters.where((master) {
      if (master.masterId == masterId) return false;
      
      // 同分类的角色
      if (master.category == targetMaster.category) return true;
      
      // 有共同专业领域的角色
      final commonSpecialties = master.specialties.where((specialty) =>
        targetMaster.specialties.contains(specialty)).toList();
      
      return commonSpecialties.isNotEmpty;
    }).toList();
    
    return similar.take(limit).toList();
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置错误信息
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// 清除错误信息
  void _clearError() {
    _error = null;
    notifyListeners();
  }

  /// 刷新数据
  Future<void> refresh() async {
    await initializeMasters();
  }
}
