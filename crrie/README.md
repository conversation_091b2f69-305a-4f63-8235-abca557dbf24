# Crrie - 家庭废品利用创意指导师

## 项目概述

Crrie 是一款专注于家庭废品利用的AI创意指导师应用，通过10个专业的AI角色，帮助用户将日常废品转化为实用物品。应用结合了环保理念与创意设计，让废品再利用变得简单有趣。

**核心理念**: 将废品转化为宝贝，让环保生活更有创意

**关键词**: Waste, Upcycling, AI, Creativity, Reuse

## 目标用户

- **环保爱好者**: 关注可持续发展，希望减少家庭废品的用户
- **创意手工爱好者**: 喜欢DIY制作，寻找新颖创意的用户
- **家庭主妇/主夫**: 希望将家庭废品变废为宝的实用主义者
- **教育工作者**: 需要环保教育素材和创意活动的老师和家长

## 应用架构

### 核心功能模块
1. **AI角色对话系统** - 10个专业废品利用指导师
2. **AI灵魂提问** - 每日互动获取金币奖励
3. **聊天历史管理** - 本地存储对话记录
4. **用户个人中心** - 个人信息与偏好管理

### 技术架构
- **状态管理**: Provider + MVP设计模式
- **AI引擎**: Moonshot文本生成模型
- **数据存储**: SQLite本地数据库
- **平台**: iOS专用（遵循Apple HIG设计规范）

## 10个AI角色设定

| 角色名称 | 英文标识 | 专业领域 | 核心能力 |
|---------|---------|---------|---------|
| 纸箱改造创意师 | CartonUpcycleCreator | 纸箱类废品 | 储物盒、儿童玩具屋、墙面置物架制作 |
| 塑料瓶利用指导师 | PlasticBottleReuseAdvisor | 塑料瓶类 | 花盆、笔筒、喂鸟器制作指导 |
| 玻璃瓶改造创意师 | GlassJarUpcycleDesigner | 玻璃容器 | 烛台、储物罐、装饰灯制作 |
| 旧衣物改造指导师 | OldClothesReuseInstructor | 纺织品类 | 购物袋、抱枕、暖手袋制作 |
| 报纸杂志利用师 | NewspaperMagazineReuseExpert | 纸质媒体 | 纸浆艺术、包装纸、拼贴画制作 |
| 瓶盖创意利用师 | BottleCapUpcycleCreator | 小型配件 | 杯垫、冰箱贴、马赛克艺术 |
| 旧家具改造指导师 | OldFurnitureUpcycleAdvisor | 家具类 | 粉刷翻新、功能改造、植物架制作 |
| 电池废品利用师 | BatteryWasteReuseInstructor | 电池类 | 安全处理、创意重用、环保回收 |
| 布料边角料利用师 | FabricScrapReuseExpert | 布料碎片 | 装饰花环、拼布被、毛绒玩具 |
| 电子废品创意师 | ElectronicWasteUpcycleDesigner | 电子产品 | 电路板艺术、键盘装饰、收纳盒 |

## 配色方案

### 主色调
- **清新绿色**: #8CC63F (环保主题色)
- **温暖橙色**: #FF8C00 (交互强调色)

### 辅助色调
- **自然米色**: #F4E1C1 (背景色)
- **蓝绿色**: #00A9A6 (平衡色)

### 功能色彩
- **深灰色**: #333333 (文本色)
- **亮黄色**: #FFEB3B (提醒色)
- **纯白色**: #FFFFFF (按钮文字)

## 核心目录架构

### Provider + MVP 分层架构
```
lib/
├── ScrapVault/           # 数据实体层 (替代Models)
│   ├── MatterCatalog/    # 废品分类数据
│   └── UpcycleBlueprint/ # 改造方案库
├── CraftAtelier/         # 界面交互层 (替代Screens)
│   ├── RemakeBench/      # 主要界面
│   └── ReuseGallery/     # 展示界面
├── UpcycleMaster/        # 业务协调层 (替代Presenters)
│   ├── DesignAlchemist/  # 方案生成器
│   └── FitMediator/      # 适配协调器
├── ReclaimFlow/          # 状态管理层 (替代State)
│   ├── ScrapStream/      # 废品分类流
│   └── CraftVault/       # 创意状态库
├── AlchemyLab/           # AI创意引擎
├── ReuseHub/             # 社交协作层
├── SalvageArchive/       # 改造知识库
├── RemakeParts/          # UI组件库
└── BlueprintRoutes/      # 配置与路由
```

## 页面结构规划

| 页面名称 | 文件路径 | 主要功能 | 技术特点 |
|---------|---------|---------|---------|
| 启动引导页 | CraftAtelier/CrrWelcomeGuide89.dart | 应用介绍、功能说明 | 轮播图、动画效果 |
| AI角色选择主页 | CraftAtelier/CrrAiMaster67Hub.dart | 10个AI角色展示、分类筛选 | 网格布局、角色卡片 |
| AI对话聊天页 | CraftAtelier/CrrChatWorkshop24.dart | 实时对话、打字机效果 | WebSocket、动画 |
| 聊天历史管理页 | CraftAtelier/CrrConversationArchive67.dart | 历史记录查看、删除管理 | 高级UI设计、搜索功能 |
| 个人中心页 | CraftAtelier/CrrUserVault45.dart | 用户信息、设置管理 | 表单组件、偏好设置 |
| AI灵魂提问模块 | RemakeParts/CrrSoulQuest78.dart | 每日问题、金币奖励 | 弹窗组件、动画效果 |
| 角色详情页 | CraftAtelier/CrrMasterDetail33.dart | AI角色介绍、能力展示 | 详情展示、交互按钮 |
| 反馈页面 | CraftAtelier/CrrFeedbackLab56.dart | 用户反馈、问题报告 | 表单提交、文件上传 |
| 设置页面 | CraftAtelier/CrrSettingsPanel82.dart | 应用设置、隐私管理 | 开关组件、选项列表 |

## 核心功能详细设计

### 1. AI灵魂提问系统
- **触发机制**: 每日首次访问个人中心页面
- **问题库**: 预设50+个深度思考问题
- **奖励机制**: 回答后获得10金币
- **UI设计**: 卡片式弹窗，温暖橙色主题

### 2. AI对话系统
- **免费次数**: 每个角色3次免费对话
- **计费方式**: 超出后需要金币购买
- **对话特色**:
  - 打字机逐字显示效果
  - 角色专属语言风格
  - 情感分析调整回复语气

### 3. 聊天记录管理
- **存储方式**: SQLite本地数据库
- **功能特性**:
  - 按角色分类显示
  - 支持关键词搜索
  - 单条/批量删除
  - 导出分享功能

### 4. 用户个人中心
- **基础信息**: 自定义头像、昵称
- **使用统计**: 对话次数、获得金币
- **偏好设置**: 收藏角色、主题切换
- **隐私管理**: 数据清理、权限设置

## 数据模型设计

### 核心实体类
```dart
// 用户信息模型
class CrrUserProfile {
  String userId;
  String nickname;
  String avatarPath;
  int totalCoins;
  DateTime lastSoulQuestionDate;
}

// AI角色模型
class CrrAiMaster {
  String masterId;
  String name;
  String englishId;
  String shortDescription;
  String longDescription;
  String avatarUrl;
  String category;
  int freeChatsRemaining;
}

// 对话记录模型
class CrrChatRecord {
  String recordId;
  String masterId;
  String userMessage;
  String aiResponse;
  DateTime timestamp;
  bool isUserMessage;
}

// 灵魂问题模型
class CrrSoulQuestion {
  String questionId;
  String questionText;
  String category;
  int rewardCoins;
  bool isAnswered;
}
```

## 技术实现细节

### Provider状态管理架构
```dart
// 主要Provider类
- CrrUserProvider: 用户信息状态管理
- CrrAiMasterProvider: AI角色数据管理
- CrrChatProvider: 对话状态管理
- CrrSoulQuestionProvider: 灵魂提问状态
```

### MVP设计模式实现
- **Model**: ScrapVault层负责数据模型定义
- **View**: CraftAtelier层负责UI界面展示
- **Presenter**: UpcycleMaster层负责业务逻辑协调

### 关键技术特性
1. **打字机效果**: 使用Timer实现逐字显示动画
2. **本地存储**: SQLite + sqflite插件
3. **网络请求**: Dio + 拦截器处理
4. **状态持久化**: SharedPreferences
5. **图片处理**: cached_network_image + image_picker
6. **动画效果**: AnimationController + Tween

### 第三方依赖规划
```yaml
dependencies:
  # 状态管理
  provider: ^6.1.1

  # 网络请求
  dio: ^5.3.2

  # 本地存储
  sqflite: ^2.3.0
  shared_preferences: ^2.2.2

  # UI组件
  cached_network_image: ^3.3.0
  image_picker: ^1.0.4

  # 动画效果
  lottie: ^2.7.0

  # 工具类
  uuid: ^4.1.0
  intl: ^0.18.1
```

## 开发状态跟踪

| 页面/组件名称 | 开发状态 | 文件路径 | 完成度 | 备注 |
|-------------|---------|---------|-------|------|
| 启动引导页 | ⏳ 待开发 | CraftAtelier/CrrWelcomeGuide89.dart | 0% | 首次启动引导 |
| AI角色选择主页 | ⏳ 待开发 | CraftAtelier/CrrAiMaster67Hub.dart | 0% | 核心功能页面 |
| AI对话聊天页 | ✅ 已完成 | CraftAtelier/CrrChatWorkshop24.dart | 100% | 实时对话、API集成、本地存储 |
| 聊天历史管理页 | ✅ 已完成 | CraftAtelier/CrrConversationArchive67.dart | 100% | 高级UI、搜索、删除功能 |
| 对话服务类 | ✅ 已完成 | UpcycleMaster/CrrConversationService84.dart | 100% | API调用、本地持久化 |
| 个人中心页 | ⏳ 待开发 | CraftAtelier/CrrUserVault45.dart | 0% | 用户管理页面 |
| AI灵魂提问模块 | ⏳ 待开发 | RemakeParts/CrrSoulQuest78.dart | 0% | 互动组件 |
| 角色详情页 | ⏳ 待开发 | CraftAtelier/CrrMasterDetail33.dart | 0% | 详情展示页面 |
| 反馈页面 | ⏳ 待开发 | CraftAtelier/CrrFeedbackLab56.dart | 0% | 用户反馈页面 |
| 设置页面 | ⏳ 待开发 | CraftAtelier/CrrSettingsPanel82.dart | 0% | 应用设置页面 |

### 数据层组件
| 组件名称 | 开发状态 | 文件路径 | 完成度 | 备注 |
|---------|---------|---------|-------|------|
| 用户数据模型 | ⏳ 待开发 | ScrapVault/CrrUserModel47.dart | 0% | 用户信息实体 |
| AI角色数据模型 | ⏳ 待开发 | ScrapVault/CrrAiMasterModel83.dart | 0% | AI角色实体 |
| 对话记录模型 | ⏳ 待开发 | ScrapVault/CrrChatModel29.dart | 0% | 聊天数据实体 |
| 数据库服务 | ⏳ 待开发 | AlchemyLab/CrrDatabaseService61.dart | 0% | SQLite操作 |
| API服务 | ⏳ 待开发 | AlchemyLab/CrrApiService94.dart | 0% | 网络请求服务 |

### Provider状态管理
| Provider名称 | 开发状态 | 文件路径 | 完成度 | 备注 |
|-------------|---------|---------|-------|------|
| 用户状态管理 | ⏳ 待开发 | ReclaimFlow/CrrUserProvider52.dart | 0% | 用户信息状态 |
| AI角色状态管理 | ⏳ 待开发 | ReclaimFlow/CrrAiMasterProvider76.dart | 0% | 角色数据状态 |
| 聊天状态管理 | ⏳ 待开发 | ReclaimFlow/CrrChatProvider38.dart | 0% | 对话状态管理 |
| 应用主题状态 | ⏳ 待开发 | ReclaimFlow/CrrThemeProvider15.dart | 0% | 主题切换状态 |

## 开发优先级

### 第一阶段 (核心功能)
1. 数据模型层搭建
2. AI角色选择主页
3. AI对话聊天页
4. 基础状态管理

### 第二阶段 (完善功能)
1. 聊天历史管理
2. 个人中心页面
3. AI灵魂提问模块
4. 本地数据存储

### 第三阶段 (优化体验)
1. 启动引导页面
2. 设置与反馈页面
3. 动画效果优化
4. 性能调优

## 开发规范与要求

### 🚨 关键开发规则 (严格遵守)

#### 1. 命名差异化要求
**禁用通用词汇**:
- ❌ home, history, profile, bottom, navigation, feedback, shop, store, purchase, buy, post, detail, coin, user
- ✅ 使用创意替代词汇、拼音、首字母缩写或混合命名

**文件命名规则**:
- 必须使用 `Crr` 作为前缀
- 采用混乱化命名策略 (截断、数字插入、拼音替换、词汇压缩)
- 示例: `CrrAiMaster67Hub.dart`, `CrrChatWorkshop24.dart`

**变量命名规则**:
- 类名、函数名、属性名都需要差异化
- 保持可读性但避免常规词汇
- 示例: `artwork_detai89`, `vi9ew`, `maanag67`

#### 2. 国际化要求
- 🌍 **应用内容**: 严格使用英文，禁止中文显示
- 📝 **代码注释**: 可使用中文
- 🖥️ **控制台输出**: 可使用中文
- ⚠️ **权限设置**: 绝对禁止中文 (审核雷区)

#### 3. 技术架构要求
- **状态管理**: Provider
- **设计模式**: MVP
- **平台**: iOS专用 (遵循Apple HIG)

#### 4. UI/UX要求
- **设计风格**: 新拟物化（Neumorphism）设计
  - 特点：模拟现实材质，按钮/卡片具有真实质感
  - 关键词：柔和、立体、极简、阴影内凹外凸
  - 背景：统一的浅色背景（如浅灰、米白色）
  - 阴影：双重阴影效果（内阴影+外阴影）
  - 按钮：内凹/外凸效果，模拟物理按压感
  - 卡片：轻微浮起效果，边缘柔和圆角
- **主页设计**: 角色卡片大图信息流，个性化设计
- **动画效果**: 使用高级动画库 (Lottie、Flutter Animate、Simple Animations)
- **字体**: 需要联网搜索并下载到本地
- **图片**: 使用Unsplash API下载到本地文件夹

#### 5. 新拟物化设计规范
- **颜色方案**:
  - 主背景色: #F0F0F3 (浅灰色)
  - 卡片背景: #F0F0F3 (与背景同色)
  - 强调色: #8CC63F (环保绿)、#FF8C00 (温暖橙)
  - 文字色: #333333 (深灰)、#666666 (中灰)
- **阴影效果**:
  - 外凸阴影: 右下深色 + 左上浅色
  - 内凹阴影: 内部深色阴影
  - 模糊半径: 8-20px
  - 偏移量: 4-8px
- **圆角规范**:
  - 小元素: 8-12px
  - 卡片: 16-24px
  - 按钮: 12-16px

### 📁 资源管理

#### Unsplash API配置
```
Application ID: 767748
Access Key: sB7G-rHWgjtvcp1Ag78GATMnmDApT_WACkbWdygsgKM
Secret Key: b-RsiPe7hsfsVDneyWel1503jBiaKFBMqCq5dUTWvAY
```

#### 本地资源路径
- **AI角色图片**: `/assets/ai/ai_1.png` 到 `/assets/ai/ai_10.png`
- **下载字体**: `/assets/fonts/`
- **下载图片**: `/assets/images/`
- **动画文件**: `/assets/animations/`

### 🎨 主页设计要求
- 使用大图展示AI角色信息
- 个性化卡片信息流布局
- 避免常规设计，突出环保创意主题
- 集成本地AI角色图片资源

---

**项目创建时间**: 2025-01-30
**最后更新时间**: 2025-01-30
**开发环境**: Flutter 3.5.4 + iOS Platform
