#!/usr/bin/env python3
"""
Unsplash图片下载脚本
用于下载Crrie应用所需的图片资源
"""

import requests
import os
import json
from urllib.parse import urlparse
import time

class UnsplashDownloader:
    def __init__(self):
        self.access_key = "sB7G-rHWgjtvcp1Ag78GATMnmDApT_WACkbWdygsgKM"
        self.base_url = "https://api.unsplash.com"
        self.headers = {
            "Authorization": f"Client-ID {self.access_key}"
        }
        
        # 创建下载目录
        self.assets_dir = "../assets"
        self.images_dir = os.path.join(self.assets_dir, "images")
        self.avatars_dir = os.path.join(self.assets_dir, "avatars")
        
        os.makedirs(self.images_dir, exist_ok=True)
        os.makedirs(self.avatars_dir, exist_ok=True)
    
    def search_photos(self, query, per_page=10):
        """搜索图片"""
        url = f"{self.base_url}/search/photos"
        params = {
            "query": query,
            "per_page": per_page,
            "orientation": "portrait"
        }
        
        response = requests.get(url, headers=self.headers, params=params)
        if response.status_code == 200:
            return response.json()
        else:
            print(f"搜索失败: {response.status_code}")
            return None
    
    def download_image(self, image_url, filename, directory):
        """下载单张图片"""
        try:
            response = requests.get(image_url)
            if response.status_code == 200:
                filepath = os.path.join(directory, filename)
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                print(f"✅ 下载成功: {filename}")
                return True
            else:
                print(f"❌ 下载失败: {filename}")
                return False
        except Exception as e:
            print(f"❌ 下载错误: {filename} - {e}")
            return False
    
    def download_avatars(self):
        """下载用户头像图片"""
        print("🔄 开始下载用户头像...")
        
        avatar_queries = [
            "portrait person smiling",
            "avatar profile picture",
            "person headshot professional",
            "user profile photo",
            "portrait photography",
            "headshot business",
            "profile picture professional",
            "avatar person",
            "portrait studio",
            "headshot portrait"
        ]
        
        for i, query in enumerate(avatar_queries, 1):
            print(f"搜索头像 {i}: {query}")
            results = self.search_photos(query, per_page=5)
            
            if results and results['results']:
                photo = results['results'][0]  # 取第一张图片
                image_url = photo['urls']['regular']
                filename = f"avatar_{i:02d}.jpg"
                
                self.download_image(image_url, filename, self.avatars_dir)
                time.sleep(1)  # 避免请求过快
    
    def download_background_images(self):
        """下载背景图片"""
        print("🔄 开始下载背景图片...")
        
        bg_queries = [
            "recycling environment green",
            "upcycling creative reuse",
            "eco friendly sustainable",
            "waste reduction creative",
            "green environment nature",
            "sustainability eco design",
            "creative workshop tools",
            "diy crafting materials",
            "environmental protection",
            "green technology innovation"
        ]
        
        for i, query in enumerate(bg_queries, 1):
            print(f"搜索背景 {i}: {query}")
            results = self.search_photos(query, per_page=3)
            
            if results and results['results']:
                photo = results['results'][0]
                image_url = photo['urls']['regular']
                filename = f"bg_{i:02d}.jpg"
                
                self.download_image(image_url, filename, self.images_dir)
                time.sleep(1)
    
    def download_all(self):
        """下载所有需要的图片"""
        print("🚀 开始下载Crrie应用图片资源...")
        print(f"📁 图片保存路径: {self.images_dir}")
        print(f"📁 头像保存路径: {self.avatars_dir}")
        
        self.download_avatars()
        self.download_background_images()
        
        print("✅ 所有图片下载完成!")

if __name__ == "__main__":
    downloader = UnsplashDownloader()
    downloader.download_all()
